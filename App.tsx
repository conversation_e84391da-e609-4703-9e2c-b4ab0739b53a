import React, {useEffect} from 'react';
import {NavigationContainer} from '@react-navigation/native';
import AppNavigator from './Src/Navigation/AppNavigation';
import {
  ActivityIndicator,
  KeyboardAvoidingView,
  LogBox,
  Platform,
  StatusBar,
  View,
} from 'react-native';
import {COLORS} from './Src/Constants/COLORS';
import {Provider} from 'react-redux';
import {PersistGate} from 'redux-persist/integration/react';
import {persistor, store} from './Src/Store/store';
import FlashMessage from 'react-native-flash-message';
import {SafeAreaProvider, SafeAreaView} from 'react-native-safe-area-context';

const App = () => {
  useEffect(() => {
    LogBox.ignoreAllLogs();
  }, []);

  return (
    <SafeAreaProvider>
      <Provider store={store}>
        <PersistGate
          loading={<ActivityIndicator color={COLORS.primary} size={'large'} />}
          persistor={persistor}>
          <SafeAreaView style={{flex: 1}}>
            <View
              style={{
                backgroundColor: COLORS.primary,
                height: Platform.OS === 'ios' ? 10 : 0,
              }}>
              <StatusBar
                barStyle="light-content"
                backgroundColor={COLORS.primary}
              />
            </View>
            <KeyboardAvoidingView
              style={{flex: 1}}
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
              keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}>
              <NavigationContainer>
                <AppNavigator />
                <FlashMessage position={'top'} />
              </NavigationContainer>
            </KeyboardAvoidingView>
          </SafeAreaView>
        </PersistGate>
      </Provider>
    </SafeAreaProvider>
  );
};

export default App;
