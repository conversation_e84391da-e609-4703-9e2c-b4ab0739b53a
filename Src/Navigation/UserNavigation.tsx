import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {RootStackParamList} from './types';
import ChooseTopic from '../Screens/ChooseTopic';
import BottomNavigation from './BottomNavigation';
import Music from '../Screens/Music';
import CourseDetail from '../Screens/CourseDetail';
import EditProfile from '../Screens/EditProfile';
import Welcome from '../Screens/WellCome';
import Welcome2 from '../Screens/WellCome2';
import {useSelector} from 'react-redux';
import {RootState} from '../Store/types';
import SignUp from '../Screens/SignUp';
import SignIn from '../Screens/SignIn';
import ForgotPassword from '../Screens/ForgotPassword';
import ConfirmPassword from '../Screens/ConfirmPassword';
import DailyBible from '../Screens/DailyBible';
import PostDetail from '../Screens/PostDetail';
import FavouritePostList from '../Screens/FavouritePostList';
import CreateCommunityScreen from '../Screens/CreateCommunityScreen';
import ViewCommunityScreen from '../Screens/ViewCommunityScreen';
import CreateCommunityPostScreen from '../Screens/CreateCommunityPostScreen';

const Stack = createNativeStackNavigator<RootStackParamList>();

const UserNavigation = () => {
  const {first, loginUser} = useSelector((state: RootState) => state?.auth);

  return (
    <Stack.Navigator
      screenOptions={{headerShown: false}}
      initialRouteName={first ? 'Welcome' : 'BottomNavigation'}>
      <Stack.Screen name="BottomNavigation" component={BottomNavigation} />
      <Stack.Screen name="Welcome" component={Welcome} />
      <Stack.Screen name="Welcome2" component={Welcome2} />
      <Stack.Screen name="ChooseTopic" component={ChooseTopic} />
      <Stack.Screen name="Music" component={Music} />
      <Stack.Screen name="CourseDetail" component={CourseDetail} />
      <Stack.Screen name="EditProfile" component={EditProfile} />
      <Stack.Screen name="DailyBible" component={DailyBible} />
      <Stack.Screen name="PostDetail" component={PostDetail} />
      <Stack.Screen name="FavouritePostList" component={FavouritePostList} />
      <Stack.Screen
        name="CreateCommunityScreen"
        component={CreateCommunityScreen}
      />
      <Stack.Screen
        name="ViewCommunityScreen"
        component={ViewCommunityScreen}
      />
      <Stack.Screen
        name="CreateCommunityPostScreen"
        component={CreateCommunityPostScreen}
      />
      {!loginUser && (
        <>
          <Stack.Screen name="SignUp" component={SignUp} />
          <Stack.Screen name="SignIn" component={SignIn} />
          <Stack.Screen name="ForgotPassword" component={ForgotPassword} />
          <Stack.Screen name="ConfirmPassword" component={ConfirmPassword} />
        </>
      )}
    </Stack.Navigator>
  );
};

export default UserNavigation;
