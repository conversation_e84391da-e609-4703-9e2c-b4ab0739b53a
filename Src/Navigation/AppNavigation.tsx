import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {RootStackParamList} from './types';
import UserNavigation from './UserNavigation';

const Stack = createNativeStackNavigator<RootStackParamList>();

const AppNavigator = () => {
  return (
    <Stack.Navigator
      initialRouteName={'UserNavigation'}
      screenOptions={{headerShown: false}}>
      <Stack.Screen name={'UserNavigation'} component={UserNavigation} />
    </Stack.Navigator>
  );
};

export default AppNavigator;
