import React, {useEffect} from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {Image, StyleSheet, Text, View} from 'react-native';
import {RootStackParamList} from './types';
import Home from '../Screens/Home';
import {SIZES} from '../Constants/SIZES';
import {COLORS} from '../Constants/COLORS';
import {ICONS} from '../Constants/IMAGES';
import Prayer from '../Screens/Prayer';
import Reminders from '../Screens/Reminders';
import Profile from '../Screens/Profile';
import Community from '../Screens/CommunityScreen';
import {useDispatch} from 'react-redux';
import {setFirst} from '../Store/Reducers/AuthReducer';

const Tab = createBottomTabNavigator<RootStackParamList>();

const BottomNavigation = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setFirst());
  }, []);

  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarHideOnKeyboard: true,
        tabBarShowLabel: false,
        tabBarIconStyle: {
          padding: 0,
          margin: 0,
        },
        tabBarStyle: {
          height: SIZES.height12,
          backgroundColor: COLORS.primary,
          width: SIZES.width,
        },
      }}>
      <Tab.Screen
        name={'Home'}
        component={Home}
        options={{
          tabBarIcon: ({focused}) => (
            <View style={styles.view}>
              <View style={[focused ? styles.focused : null, styles.pad]}>
                <Image
                  source={ICONS.home}
                  style={[
                    styles.icon,
                    {tintColor: focused ? COLORS.white : COLORS.gray4},
                  ]}
                />
              </View>
              <Text
                style={[
                  styles.text,
                  {color: focused ? COLORS.purple : COLORS.gray5},
                ]}>
                Home
              </Text>
            </View>
          ),
        }}
      />
      <Tab.Screen
        name={'Reminders'}
        component={Reminders}
        options={{
          tabBarIcon: ({focused}) => (
            <View style={styles.view}>
              <View style={[focused ? styles.focused : null, styles.pad]}>
                <Image
                  source={ICONS.sleep}
                  style={[
                    styles.icon,
                    {tintColor: focused ? COLORS.white : COLORS.gray4},
                  ]}
                />
              </View>
              <Text
                style={[
                  styles.text,
                  {color: focused ? COLORS.purple : COLORS.gray5},
                ]}>
                Sleep
              </Text>
            </View>
          ),
        }}
      />
      <Tab.Screen
        name={'Prayer'}
        component={Prayer}
        options={{
          tabBarIcon: ({focused}) => (
            <View style={styles.view}>
              <View style={[focused ? styles.focused : null, styles.pad]}>
                <Image
                  source={ICONS.meditate}
                  style={[
                    styles.icon,
                    {tintColor: focused ? COLORS.white : COLORS.gray4},
                  ]}
                />
              </View>
              <Text
                style={[
                  styles.text,
                  {color: focused ? COLORS.purple : COLORS.gray5},
                ]}>
                Meditate
              </Text>
            </View>
          ),
        }}
      />
      <Tab.Screen
        name={'Community'}
        component={Community}
        options={{
          tabBarIcon: ({focused}) => (
            <View style={styles.view}>
              <View style={[focused ? styles.focused : null, styles.pad]}>
                <Image
                  source={ICONS.communities}
                  style={[
                    styles.icon,
                    {tintColor: focused ? COLORS.white : COLORS.gray4},
                  ]}
                />
              </View>
              <Text
                style={[
                  styles.text,
                  {color: focused ? COLORS.purple : COLORS.gray5},
                ]}>
                Community
              </Text>
            </View>
          ),
        }}
      />
      <Tab.Screen
        name={'Profile'}
        component={Profile}
        options={{
          tabBarIcon: ({focused}) => (
            <View style={styles.view}>
              <View style={[focused ? styles.focused : null, styles.pad]}>
                <Image
                  source={ICONS.user}
                  style={[
                    styles.icon,
                    {tintColor: focused ? COLORS.white : COLORS.gray4},
                  ]}
                />
              </View>
              <Text
                style={[
                  styles.text,
                  {color: focused ? COLORS.purple : COLORS.gray5},
                ]}>
                Profile
              </Text>
            </View>
          ),
        }}
      />
    </Tab.Navigator>
  );
};

export default BottomNavigation;

const styles = StyleSheet.create({
  pad: {
    padding: 10,
  },
  focused: {
    backgroundColor: COLORS.purple,
    borderRadius: 15,
  },
  view: {
    width: SIZES.width20,
    alignItems: 'center',
    marginTop: SIZES.width06,
  },
  text: {
    fontSize: SIZES.width035,
  },
  icon: {
    width: SIZES.width07,
    height: SIZES.width07,
    resizeMode: 'contain',
  },
});
