import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../Navigation/types';
import {SIZES} from '../Constants/SIZES';
import {COLORS} from '../Constants/COLORS';
import {IMAGES} from '../Constants/IMAGES';

type NavProp = NativeStackNavigationProp<RootStackParamList>;

const PostCards = ({item, width}: any) => {
  const nav = useNavigation<NavProp>();
  const [err, setErr] = useState(false);

  return (
    <TouchableOpacity
      style={[styles.postCardCont, {width: width ? width : SIZES.width60}]}
      onPress={() => nav.navigate('PostDetail', {id: item.id})}>
      <Image
        source={err ? IMAGES.eventDefault : {uri: item?.image}}
        style={styles.postCard}
        onError={() => setErr(!err)}
      />
      <Text style={styles.postTitle}>{item?.title}</Text>
    </TouchableOpacity>
  );
};

export default PostCards;

const styles = StyleSheet.create({
  postTitle: {
    color: COLORS.white,
    fontSize: SIZES.width045,
    marginVertical: 10,
  },
  postCard: {
    width: '100%',
    height: SIZES.height20,
    borderRadius: 15,
  },
  postCardCont: {
    width: SIZES.width60,
  },
});
