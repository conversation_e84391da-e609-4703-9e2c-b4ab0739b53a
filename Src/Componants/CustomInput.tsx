import React from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  Image,
  StyleSheet,
  TextInputProps,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native';
import {SIZES} from '../Constants/SIZES';

type Props = TextInputProps & {
  value: string;
  setValue: (val: string) => void;
  secure?: boolean;
  editable?: boolean;
  setSecure?: (b: boolean) => void;
  rightIcon?: any;
  containerStyle?: StyleProp<ViewStyle>;
  inputStyle?: StyleProp<TextStyle>;
  auth?: boolean;
};

const CustomTextInput: React.FC<Props> = ({
  value,
  setValue,
  secure,
  setSecure,
  rightIcon,
  containerStyle,
  inputStyle,
  editable,
  placeholder,
  placeholderTextColor,
  ...props
}) => (
  <View style={[styles.container, containerStyle]}>
    <TextInput
      value={value}
      onChangeText={setValue}
      secureTextEntry={secure}
      style={[styles.input, inputStyle]}
      editable={editable}
      placeholder={placeholder}
      placeholderTextColor={placeholderTextColor}
      {...props}
    />
    {rightIcon && setSecure && (
      <TouchableOpacity onPress={() => setSecure(!secure)}>
        <Image source={rightIcon} style={styles.icon} />
      </TouchableOpacity>
    )}
  </View>
);

export default CustomTextInput;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#ddd',
    paddingHorizontal: 12,
    marginVertical: 5,
    width: '100%',
  },
  input: {
    flex: 1,
    fontSize: SIZES.width045,
    paddingVertical: 12,
    color: '#000',
  },
  icon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
});
