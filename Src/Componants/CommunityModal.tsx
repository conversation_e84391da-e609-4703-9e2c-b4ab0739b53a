import React, {useState} from 'react';
import {
  ImageBackground,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {SIZES} from '../Constants/SIZES';
import {COLORS} from '../Constants/COLORS';
import CustomButton from './CustomButton';
import {useSelector} from 'react-redux';
import {RootState} from '../Store/types';
import {CommunityJoinAPI} from '../Store/Actions/CommunityActions';
import Loader from './Loader';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../Navigation/types';
import {showError} from '../Constants/FlashMessage';

type NavProp = NativeStackNavigationProp<RootStackParamList>;

const CommunityModal = ({
  modalVisible,
  setModalVisible,
  comData,
  setComData,
  loginUser,
}: any) => {
  const nav = useNavigation<NavProp>();
  const {id} = useSelector((state: RootState) => state.auth.userDetails);
  const [load, setLoad] = useState(false);

  const closeModal = () => {
    setComData(null);
    setModalVisible(false);
  };

  const joinCom = async () => {
    if (!loginUser) return showError('Login to continue');
    const data = {
      user_id: id,
      community_id: comData?.id,
      status: 1,
    };
    await CommunityJoinAPI(data, setLoad, nav, closeModal);
  };

  return (
    <Modal
      animationType={'fade'}
      transparent={true}
      visible={modalVisible}
      onRequestClose={closeModal}>
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          {load ? (
            <Loader color={COLORS.primary} />
          ) : (
            <ScrollView>
              <ImageBackground
                source={{uri: comData?.image}}
                style={styles.image}>
                <TouchableOpacity style={styles.crossBtn} onPress={closeModal}>
                  <Text style={styles.cross}>X</Text>
                </TouchableOpacity>
              </ImageBackground>
              <View style={styles.inner}>
                <Text style={styles.modalText}>{comData?.name}</Text>
                <Text style={styles.modaldesc}>{comData?.description}</Text>
                <Text style={styles.modaldesc}>
                  Category: {comData?.community_category?.name}
                </Text>
                {comData?.reasons?.map((val: any) => (
                  <Text style={styles.modaldesc}>• {val?.description}</Text>
                ))}
              </View>
              <CustomButton
                onPress={joinCom}
                title={'Join'}
                auth
                bgColor={COLORS.purple}
                width={SIZES.width80}
                color={COLORS.white}
              />
            </ScrollView>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default CommunityModal;

const styles = StyleSheet.create({
  modaldesc: {
    fontSize: SIZES.width04,
    color: COLORS.black,
    marginTop: 5,
  },
  inner: {
    padding: 20,
  },
  image: {
    width: '100%',
    height: SIZES.height25,
    resizeMode: 'cover',
    zIndex: 0,
  },
  cross: {
    color: COLORS.white,
    fontSize: SIZES.width06,
  },
  crossBtn: {
    position: 'absolute',
    top: 30,
    right: 30,
    borderWidth: 1,
    borderRadius: 50,
    zIndex: 10,
    width: SIZES.width10,
    height: SIZES.width10,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: COLORS.white,
  },
  modalText: {
    fontSize: SIZES.width05,
    fontWeight: 'bold',
    color: COLORS.black,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    margin: 20,
    backgroundColor: COLORS.white,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: SIZES.width90,
    height: SIZES.height60,
    overflow: 'hidden',
  },
});
