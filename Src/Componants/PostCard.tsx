import React, {useRef} from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import {getYouTubeVideoId} from '../Constants/FlashMessage';
import YoutubeIframe from 'react-native-youtube-iframe';
import {SIZES} from '../Constants/SIZES';
import {COLORS} from '../Constants/COLORS';
import AudioComp from './AudioComp';

const PostCard = ({item}: any) => {
  const playerRef = useRef(null);
  const videoId = getYouTubeVideoId(item?.url);

  return (
    <>
      <View style={styles.cont}>
        {videoId && (
          <YoutubeIframe
            ref={playerRef}
            height={SIZES.height25}
            width={SIZES.width90}
            play={false}
            videoId={videoId}
          />
        )}
        {item?.image && (
          <Image source={{uri: item?.image}} style={styles.image} />
        )}
        <Text style={styles.title}>{item.title}</Text>
        <Text style={styles.desc}>{item.description}</Text>
        {item?.audio && <AudioComp item={item?.audio} />}
      </View>
      <View style={styles.line} />
    </>
  );
};

export default PostCard;

const styles = StyleSheet.create({
  image: {
    resizeMode: 'cover',
    width: '100%',
    height: SIZES.width40,
    borderRadius: 10,
    marginBottom: 15,
  },
  desc: {
    fontSize: SIZES.width04,
    color: COLORS.white,
    marginBottom: 10,
  },
  title: {
    fontSize: SIZES.width05,
    color: COLORS.white,
  },
  line: {
    width: SIZES.width70,
    marginVertical: 15,
    borderBottomWidth: 1,
    borderColor: COLORS.white,
    alignSelf: 'center',
  },
  cont: {
    alignSelf: 'center',
    width: SIZES.width90,
    marginTop: 10,
  },
});
