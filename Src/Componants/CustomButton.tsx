import React from 'react';
import {StyleSheet, Text, TouchableOpacity} from 'react-native';
import {COLORS} from '../Constants/COLORS';
import {SIZES} from '../Constants/SIZES';

type CustomButtonProps = {
  title: string;
  bgColor?: string;
  color?: string;
  auth?: boolean;
  loading?: boolean;
  width?: number;
  onPress?: () => void;
};

const CustomButton = ({
  title,
  auth,
  onPress,
  bgColor,
  color,
  width,
}: CustomButtonProps) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[
        auth
          ? {
              backgroundColor: COLORS.white,
              width: SIZES.width90,
              padding: 15,
              alignItems: 'center',
              alignSelf: 'center',
              borderRadius: 30,
              marginVertical: 20,
            }
          : {},
        bgColor ? {backgroundColor: bgColor} : {},
        width ? {width: width} : {},
      ]}>
      <Text
        style={[
          auth ? {fontSize: SIZES.width05} : {},
          color ? {color: color} : {color: COLORS.black},
        ]}>
        {title}
      </Text>
    </TouchableOpacity>
  );
};

export default CustomButton;

const styles = StyleSheet.create({});
