import React, {useEffect, useState} from 'react';
import {
  DeviceEventEmitter,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import SoundPlayer from 'react-native-sound-player';
import {ICONS} from '../Constants/IMAGES';
import {SIZES} from '../Constants/SIZES';
import {COLORS} from '../Constants/COLORS';

const AudioComp = ({item}: any) => {
  const [position, setPosition] = useState(0);
  const [play, setPlay] = useState(false);

  const playSound = () => {
    try {
      if (play) {
        SoundPlayer.pause();
        setPlay(false);
      } else {
        SoundPlayer.playUrl(item);
        setPlay(true);
      }
    } catch (e) {
      console.error('Error in play/pause:', e);
    }
  };

  const forward10 = () => {
    SoundPlayer.seek(position + 15);
  };

  const rewind10 = () => {
    SoundPlayer.seek(Math.max(position - 15, 0));
  };

  useEffect(() => {
    const interval = setInterval(() => {
      try {
        SoundPlayer.getInfo().then(info => {
          setPosition(info.currentTime);
        });
      } catch (e) {
        console.log('error getting audio info', e);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const onEndSub = DeviceEventEmitter.addListener(
      'FinishedPlaying',
      ({success}) => {
        console.log('Finished playing!', success);
        setPlay(false);
        setPosition(0);
      },
    );

    return () => onEndSub.remove();
  }, []);

  return (
    <View style={styles.row}>
      <TouchableOpacity onPress={rewind10}>
        <Image source={ICONS.rewind} style={styles.img} />
      </TouchableOpacity>
      <TouchableOpacity onPress={playSound}>
        <Image
          source={play ? ICONS.pause : ICONS.play}
          style={[styles.img, {tintColor: COLORS.white}]}
        />
      </TouchableOpacity>
      <TouchableOpacity onPress={forward10}>
        <Image
          source={ICONS.rewind}
          style={[styles.img, {transform: [{rotateY: '180deg'}]}]}
        />
      </TouchableOpacity>
    </View>
  );
};

export default AudioComp;

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-evenly',
    marginVertical: 15,
    
  },
  img: {
    resizeMode: 'contain',
    width: SIZES.width10,
    height: SIZES.width10,
  },
});
