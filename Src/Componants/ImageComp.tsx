import React, {useState} from 'react';
import {Image, StyleSheet} from 'react-native';
import {SIZES} from '../Constants/SIZES';
import {IMAGES} from '../Constants/IMAGES';

const ImageComp = ({item}: any) => {
  const [err, setErr] = useState(false);

  return (
    <Image
      source={err ? IMAGES.eventDefault : {uri: item?.url}}
      style={styles.image}
      onError={() => setErr(!err)}
    />
  );
};

export default ImageComp;

const styles = StyleSheet.create({
  image: {
    width: SIZES.width,
    height: SIZES.height30,
    resizeMode: 'cover',
    marginVertical: 15,
  },
});
