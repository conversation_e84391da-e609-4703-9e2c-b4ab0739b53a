export const EndPoints = {
  baseUrl: `https://api.lightofthewords.com/`,
  // baseUrl: `https://service.demowebsitelinks.com:3013/`, 
  // baseUrl: `https://lightofthewords.com:3013/`,
  login: `auth/login`,
  me: `auth/me`,
  signUp: `auth/signup`,
  forgotPass: `auth/forgot-password`,
  submitOtp: `auth/submit-otp`,
  categories: `categories?page=`,
  categoriesDetails: `categories/`,
  featured: `posts/get/featured-posts`,
  posts: `posts`,
  resetPassword: `auth/reset-password`,
  fav: `posts/add-to-favourites/`,
  favList: `posts/favourites/list`,
  updateProfile: `auth/update-profile`,
  uploadProfilePicture: '/auth/upload-profile-picture',
  communities: `communities`,
  communitiesCategory: `community-categories`,
  reasons: `reasons`,
  userJoinedCoomunity: `community-joins/user/`,
  communityPost: `community-posts`,
  communityJoin: `community-joins`,
musicCategories: `music-categories`,
musicCatBaseURL: `https://service.demowebsitelinks.com/uploads/music-categories/`,
  musicCategoryById: (id: string | number) => `music-categories/${id}`,
  musicById: (id: string | number) => `music/${id}`,
  music: 'music',
};


export const BibleRoutes = {
  baseUrl: `https://api.scripture.api.bible/v1/`,
  bibles: `bibles`,
  lang: `?language=eng`,
  books: `books`,
  chapters: `chapters`,
};



export default EndPoints; 