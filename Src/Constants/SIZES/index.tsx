import {Dimensions} from 'react-native';

const {width, height} = Dimensions.get('window');

export const SIZES = {
  width: width,
  width02: width * 0.02,
  width03: width * 0.03,
  width035: width * 0.035,
  width04: width * 0.04,
  width045: width * 0.045,
  width05: width * 0.05,
  width06: width * 0.06,
  width07: width * 0.07,
  width09: width * 0.09,
  width10: width * 0.1,
  width12: width * 0.12,
  width13: width * 0.13,
  width15: width * 0.15,
  width20: width * 0.2,
  width25: width * 0.25,
  width30: width * 0.3,
  width40: width * 0.4,
  width45: width * 0.45,
  widthHalf: width * 0.5,
  width60: width * 0.6,
  width65: width * 0.65,
  width67: width * 0.67,
  width68: width * 0.68,
  width70: width * 0.7,
  width80: width * 0.8,
  width90: width * 0.9,

  height: height,
  height02: height * 0.02,
  height05: height * 0.05,
  height10: height * 0.1,
  height12: height * 0.12,
  height15: height * 0.15,
  height20: height * 0.2,
  height25: height * 0.25,
  height30: height * 0.3,
  height35: height * 0.35,
  height40: height * 0.4,
  heightHalf: height * 0.5,
  height55: height * 0.55,
  height60: height * 0.6,
  height70: height * 0.7,
  height80: height * 0.8,
  height85: height * 0.85,
  height90: height * 0.9,
};
