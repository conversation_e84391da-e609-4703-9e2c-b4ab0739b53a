import BibleAPI from '../../../Constants/API/BibleAPI';
import {BibleRoutes} from '../../../Constants/Routes';
import {showError} from '../../Helpers/Utils';

export const getBibleAPI = async (setData, setLoad) => {
  await BibleAPI.get(BibleRoutes.bibles + BibleRoutes.lang)
    .then(e => {
      const filteredData = e?.data?.data?.filter(
        item =>
          item?.id === 'de4e12af7f28f599-01' ||
          item?.id === '685d1470fe4d5c3b-01',
      );
      setData(filteredData);
    })
    .catch(err => {
      console.error('getBibleAPI err', err);
      showError('Something went wrong');
    })
    .finally(() => {
      setLoad(false);
    });
};

export const getBibleBooksAPI = async (id, setData, setLoad) => {
  await BibleAPI.get(BibleRoutes.bibles + `/${id}/` + BibleRoutes.books)
    .then(e => {
      setData(e?.data?.data);
    })
    .catch(err => {
      console.error('getBibleBooksAPI err', err);
      showError('Something went wrong');
    })
    .finally(() => {
      setLoad(false);
    });
};

export const getBibleBooksChaptersAPI = async (
  bibleId,
  bookId,
  setData,
  setLoad,
) => {
  await BibleAPI.get(
    BibleRoutes.bibles +
      `/${bibleId}/` +
      BibleRoutes.books +
      `/${bookId}/` +
      BibleRoutes.chapters,
  )
    .then(e => {
      setData(e?.data?.data);
    })
    .catch(err => {
      console.error('getBibleBooksChaptersAPI err', err);
      showError('Something went wrong');
    })
    .finally(() => {
      setLoad(false);
    });
};

export const getChaptersContentAPI = async (
  bibleId,
  chapId,
  setData,
  setLoad,
) => {
  await BibleAPI.get(
    BibleRoutes.bibles + `/${bibleId}/` + BibleRoutes.chapters + `/${chapId}`,
  )
    .then(e => {
      setData(e?.data?.data);
    })
    .catch(err => {
      console.error('getBibleBooksChaptersAPI err', err);
      showError('Something went wrong');
    })
    .finally(() => {
      setLoad(false);
    });
};
