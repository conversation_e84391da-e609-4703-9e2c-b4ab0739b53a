import API from '../../../Constants/API';
import {showError, showSuccess} from '../../../Constants/FlashMessage';
import {EndPoints} from '../../../Constants/Routes';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const CategoriesAPI = async (
  page: string,
  setCat: React.Dispatch<React.SetStateAction<any[]>>,
  setCatPage: any,
  setLoadCat: (arg: boolean) => void,
) => {
  API.get(EndPoints.categories + page)
    .then(e => {
      if (e?.data?.success) {
        setCat(prev => [...prev, ...e.data.data]);
        setCatPage({
          current: parseInt(e?.data?.currentPage),
          total: e?.data?.totalPages,
        });
      } else {
        showError(e?.data?.message || '');
      }
    })
    .catch(err => {
      console.error('CategoriesAPI error->', err);
      console.error('CategoriesAPI error->', err?.response);
      showError(err?.response?.data?.message || 'Something went wrong');
    })
    .finally(() => setLoadCat(false));
};

export const CategoriesDeatilAPI = async (
  id: number,
  setCat: any,
  setLoad: (arg: boolean) => void,
) => {
  API.get(EndPoints.categoriesDetails + id)
    .then(e => {
      if (e?.data?.success) {
        setCat(e.data.data);
      } else {
        showError(e?.data?.message || '');
      }
    })
    .catch(err => {
      console.error('CategoriesDeatilAPI error->', err);
      console.error(
        'CategoriesDeatilAPI error->',
        err?.response?.data?.message,
      );
      showError(err?.response?.data?.message || 'Something went wrong');
    })
    .finally(() => setLoad(false));
};

export const featuredPostAPI = async (setPost: any) => {
  // try {
  //   const res = await API(EndPoints.featured, {method: 'GET'});

  //   if (res.success) {
  //     setPost(res.data);
  //   } else {
  //     showError(res.message || '');
  //   }
  // } catch (err: any) {
  //   console.error('featuredPostAPI error->', err);
  //   console.error('featuredPostAPI error->', err?.response?.data?.message);
  //   showError(err?.response?.data?.message || 'Something went wrong');
  // }
  API.get(EndPoints.featured)
    .then(e => {
      if (e?.data?.success) {
        setPost(e.data.data);
      } else {
        showError(e?.data?.message || '');
      }
    })
    .catch(err => {
      console.error('featuredPostAPI error->', err);
      console.error('featuredPostAPI error->', err?.response?.data?.message);
      showError(err?.response?.data?.message || 'Something went wrong');
    });
};

export const featuredPostDetailAPI = async (
  id: number,
  setPost: any,
  setLoad: (arg: boolean) => void,
) => {
  API.get(EndPoints.posts + '/' + id)
    .then(e => {
      if (e?.data?.success) {
        setPost(e.data.data);
      } else {
        showError(e?.data?.message || '');
      }
    })
    .catch(err => {
      console.error('featuredPostDetailAPI error->', err);
      console.error(
        'featuredPostDetailAPI error->',
        err?.response?.data?.message,
      );
      showError(err?.response?.data?.message || 'Something went wrong');
    })
    .finally(() => setLoad(false));
};

export const addFavAPI = async (
  id: number,
  setLoad: (arg: boolean) => void,
) => {
  API.post(EndPoints.fav + id)
    .then(e => {
      if (e?.data?.success) {
        showSuccess(e.data.message);
      } else {
        showError(e?.data?.message || '');
      }
    })
    .catch(err => {
      console.error('addFavAPI error->', err);
      console.error('addFavAPI error->', err?.response?.data?.message);
      showError(err?.response?.data?.message || 'Something went wrong');
    })
    .finally(() => setLoad(false));
};

export const listFavAPI = async (
  setPost: any,
  setLoad: (arg: boolean) => void,
) => {
  API.get(EndPoints.favList)
    .then(e => {
      if (e?.data?.success) {
        setPost(e.data.data);
      } else {
        showError(e?.data?.message || '');
      }
    })
    .catch(err => {
      console.error('listFavAPI error->', err);
      console.error('listFavAPI error->', err?.response?.data?.message);
      showError(err?.response?.data?.message || 'Something went wrong');
    })
    .finally(() => setLoad(false));
};




export const getMusicCategories = async (
  setMusicCategories: (val: any[]) => void,
  setSel: (val: any) => void,
) => {


  try {

    const response = await API.get('/music-categories');


    const categories = response.data || [];


    if (categories.length === 0) {

    }

    setMusicCategories(categories);
    setSel(categories[0]?.id || null);

   
  } catch (error: any) {
    
  }
};

export const getMusicCategoryById = async (
  id: number,
  setMusics: (val: any[]) => void
) => {
  console.log('🎯 Fetching musics for category ID:', id);

  try {
    const token = await AsyncStorage.getItem('token');
    if (!token) {
      return;
    }

    const response = await API.get(`/music-categories/${id}`, { 
    });

    const musics = response.data?.musics || [];

    console.log('🎶 Musics fetched:', musics.length);
    setMusics(musics);
  } catch (error: any) {
  }
};
  

export const musicByIdAPI = async (
  id: number,
  setMusic: (data: any) => void,
  setLoading: (loading: boolean) => void,
) => {

  try {
    const response = await API.get(EndPoints.musicById(id));

    if (response?.data) {
      setMusic(response?.data);
    } else {
      showError(response?.data?.message || 'Something went wrong');
    }
  } catch (err: any) {
    showError(err?.response?.data?.message || 'Something went wrong');
  } finally {
    setLoading(false);
  }
};
export const getHomeTopCards = async (
  setData: (data: any[]) => void,
  setLoading: (arg: boolean) => void,
) => {
  try {
    const response = await API.get('/music');
    const all = response?.data || [];

    const topTwo = all.slice(0, 2); 
    setData(topTwo);
} catch (err: any) {
  showError(err?.response?.data?.message || 'Something went wrong');
} finally {
    setLoading(false);
  }
};
