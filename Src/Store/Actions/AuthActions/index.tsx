import API from '../../../Constants/API';
import {showError, showSuccess} from '../../../Constants/FlashMessage';
import {EndPoints} from '../../../Constants/Routes';
import {
  setLoginUser,
  setTokenId,
  setUserDetails,
} from '../../Reducers/AuthReducer';
import {store} from '../../store';



export const LoginUserAPI = async (
  data: any,
  setLoad: (value: boolean) => void,
) => {
  API.post(EndPoints.login, data)
    .then(async e => {
      if (e?.data?.success) {
        const token = e?.data?.data?.access_token;
        store.dispatch(setTokenId(token));

        MyDeatilsAPI(setLoad);
      } else {
        showError(e?.data?.message || '');
        setLoad(false);
      }
    })
    .catch(err => {
      console.error('❌ LoginAPI error:', err);
      console.error('❌ Error message:', err?.response?.data?.message);
      showError(err?.response?.data?.message || 'Something went wrong');
      setLoad(false);
    });
};
export const MyDeatilsAPI = async (setLoad: (value: boolean) => void) => {
  API.get(EndPoints.me)
    .then(e => {
      if (e?.data?.success) {
        showSuccess('Login Succesfully');
        store.dispatch(setUserDetails(e?.data?.data));
        store.dispatch(setLoginUser());
      } else {
        showError(e?.data?.message || '');
      }
    })
    .catch(err => {
      console.error('MyDeatilsAPI error->', err);
      console.error('MyDeatilsAPI error->', err?.response?.data?.message);
      showError(err?.response?.data?.message || 'Something went wrong');
    })
    .finally(() => setLoad(false));
};

export const SignUpUserAPI = async (
  data: any,
  setLoad: (value: boolean) => void,
  navigation: any,
) => {
  API.post(EndPoints.signUp, data)
    .then(e => {
      if (e?.data?.success) {
        showSuccess(e?.data?.message);
        navigation.navigate('SignIn');
      } else {
        showError(e?.data?.message || '');
      }
    })
    .catch(err => {
      console.error('SignUpUserAPI error->', err);
      console.error('SignUpUserAPI error->', err?.response?.data?.message);
      showError(err?.response?.data?.message || 'Something went wrong');
      setLoad(false);
    });
};

export const SendCodeAPI = async (
  data: any,
  setLoad: (value: boolean) => void,
  setFirst: (value: boolean) => void,
) => {
  API.post(EndPoints.forgotPass, data)
    .then(e => {
      if (e?.data?.success) {
        showSuccess(e?.data?.message);
        setFirst(true);
      } else {
        showError(e?.data?.message || '');
      }
    })
    .catch(err => {
      console.error('SendCodeAPI error->', err);
      console.error('SendCodeAPI error->', err?.response?.data?.message);
      showError(err?.response?.data?.message || 'Something went wrong');
      setLoad(false);
    })
    .finally(() => setLoad(false));
};

export const SubmitOtpAPI = async (
  data: any,
  setLoad: (value: boolean) => void,
  onSuccess: () => void,
) => {
  API.post(EndPoints.submitOtp, data)
    .then(res => {
      if (res?.data?.success) {
        showSuccess(res?.data?.message);
        onSuccess();
      } else {
        showError(res?.data?.message || 'Invalid code');
      }
    })
    .catch(err => {
      console.error('SubmitOtpAPI error ->', err?.message);
      showError(err?.response?.data?.message || 'Something went wrong');
    })
    .finally(() => setLoad(false));
};

export const ResetPasswordAPI = async (
  data: {email: string; password: string},
  setLoad: (value: boolean) => void,
  navigation: any,
) => {
  API.post(EndPoints.resetPassword, data)
    .then(res => {
      if (res?.data?.success) {
        showSuccess(res?.data?.message || 'Password updated successfully');
        navigation.navigate('SignIn');
      } else {
        showError(res?.data?.message || 'Something went wrong');
      }
    })
    .catch(err => {
      console.error('ResetPasswordAPI error ->', err);
      console.error('ResetPasswordAPI error ->', err?.response?.data?.message);
      showError(err?.response?.data?.message || 'Something went wrong');
    })
    .finally(() => setLoad(false));
};

export const UpdateProfileAPI = async (
  payload: any,
  setLoading: (val: boolean) => void,
  onSuccess: () => void,
) => {
  API.post(EndPoints.updateProfile, payload)
    .then(res => {
      if (res?.data?.success) {
        showSuccess(res?.data?.message || 'Profile Updated');
        store.dispatch(setUserDetails(res?.data?.data));
        onSuccess();
      } else {
        showError(res?.data?.message || 'Something went wrong');
      }
    })
    .catch(err => {
      console.error('UpdateProfileAPI Error:', err);
      showError(err?.response?.data?.message || 'Something went wrong');
      setLoading(false);
    });
};

export const UploadProfilePictureAPI = async (
  image: {
    uri: string;
    name: string;
    type: string;
  },
  setLoading: (val: boolean) => void,
  onSuccess: () => void,
) => {
  const formData = new FormData();
  formData.append('profile_picture', {
    uri: image.uri,
    name: image.name,
    type: image.type,
  } as any);

  API.post(EndPoints.uploadProfilePicture, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
    .then(res => {
      if (res?.data?.success) {
        showSuccess(res?.data?.message || 'Profile picture updated');
        store.dispatch(setUserDetails(res?.data?.data));
        onSuccess();
      } else {
        showError(res?.data?.message || 'Upload failed');
      }
    })
    .catch(err => {
      console.error('UploadProfilePictureAPI Error:', err);
      showError(err?.response?.data?.message || 'Something went wrong');
    })
    .finally(() => setLoading(false));
};

import axios from 'axios';

type Community = {
  id: number;
  name: string;
  description: string;
  image?: string;
  reasons?: {id: number; description: string}[];
  community_category?: {id: number; name: string; image: string} | null;
};

export const GetCommunitiesAPI = async (
  onSuccess: (data: Community[]) => void,
) => {
  try {
    const res = await API.get<Community[]>('/communities');
    onSuccess(res.data);
  } catch (err: unknown) {
    if (axios.isAxiosError(err)) {
      console.log('Axios Error:', err.message);

      if (err.response) {
        console.log('Server Error:', err.response.data);
      } else if (err.request) {
        console.log('No Response Received:', err.request);
      }
    } else if (err instanceof Error) {
      console.log('Other Error:', err.message);
    } else {
      console.log('Unknown error:', err);
    }
  }
};
