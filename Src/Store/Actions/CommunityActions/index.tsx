import API from '../../../Constants/API';
import {showError, showSuccess} from '../../../Constants/FlashMessage';
import {EndPoints} from '../../../Constants/Routes';

export const CommunityCategoriesAPI = async (
  setCat: React.Dispatch<React.SetStateAction<any[]>>,
) => {
  API.get(EndPoints.communitiesCategory)
    .then(e => {
      const formattedItems = e?.data?.map((item: any) => ({
        label: item.name,
        value: item.id,
      }));
      setCat(formattedItems);
    })
    .catch(err => {
      console.error('CommunityCategoriesAPI error->', err);
      console.error(
        'CommunityCategoriesAPI error->',
        err?.response?.data?.message,
      );
      showError(err?.response?.data?.message || 'Something went wrong');
    });
};

export const CommunityReasonAPI = async (
  setReason: React.Dispatch<React.SetStateAction<any[]>>,
) => {
  API.get(EndPoints.reasons)
    .then(e => {
      const formattedItems = e?.data?.map((item: any) => ({
        label: item.description,
        value: item.id,
      }));
      setReason(formattedItems);
    })
    .catch(err => {
      console.error('CommunityReasonAPI error->', err);
      console.error('CommunityReasonAPI error->', err?.response?.data?.message);
      showError(err?.response?.data?.message || 'Something went wrong');
    });
};

export const CreateCommunityAPI = async (data: any, nav: any) => {
  API.post(EndPoints.communities, data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
    .then(e => {
      showSuccess(e?.data?.message);
      nav.goBack();
    })
    .catch(err => {
      console.error('CreateCommunityAPI error->', err);
      console.error('CreateCommunityAPI error->', err?.response?.data?.message);
      showError(err?.response?.data?.message || 'Something went wrong');
    });
};

export const userJoinedCommunities = async (id: string, setJoined: any) => {
  API.get(EndPoints.userJoinedCoomunity + id)
    .then(e => {
      setJoined(e.data.data);
    })
    .catch(err => {
      console.error('userJoinedCommunities error->', err);
      console.error(
        'userJoinedCommunities error->',
        err?.response?.data?.message,
      );
      showError(err?.response?.data?.message || 'Something went wrong');
    });
};

export const CommunityInfoAPI = async (
  id: string,
  setData: any,
  setLoad: (loading: boolean) => void,
) => {
  await API.get(EndPoints.communities + '/' + id)
    .then(e => {
      setData(e.data);
    })
    .catch(err => {
      console.error('CommunityInfoAPI error->', err?.response?.data);
      showError(err?.response?.data?.message || 'Something went wrong');
    })
    .finally(() => setLoad(false));
};

export const CommunityPostAPI = async (
  data: any,
  setLoad: (loading: boolean) => void,
  nav: any,
) => {
  await API.post(EndPoints.communityPost, data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
    .then(e => {
      // setData(e.data);
      console.log('CommunityPostAPI res', e.data);
      // nav.goBack();
    })
    .catch(err => {
      console.error('CommunityPostAPI error->', err);
      console.error('CommunityPostAPI error->', err?.response?.data);
      showError(err?.response?.data?.message || 'Something went wrong');
    })
    .finally(() => setLoad(false));
};

export const CommunityJoinAPI = async (
  data: any,
  setLoad: (loading: boolean) => void,
  nav: any,
  closeModal: () => void,
) => {
  await API.post(EndPoints.communityJoin, data)
    .then(e => {
      showSuccess(e?.data?.message);
      closeModal();
      nav.navigate('ViewCommunityScreen', {id: e?.data?.community?.id});
      console.log('CommunityJoinAPI res', e.data);
    })
    .catch(err => {
      console.error('CommunityJoinAPI error->', err);
      console.error('CommunityJoinAPI error->', err?.response?.data);
      showError(err?.response?.data?.message || 'Something went wrong');
    })
    .finally(() => setLoad(false));
};
