import React, {useState} from 'react';
import {
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {SIZES} from '../Constants/SIZES';
import {COLORS} from '../Constants/COLORS';
import {IMAGES} from '../Constants/IMAGES';
import {launchImageLibrary} from 'react-native-image-picker';
import CustomButton from '../Componants/CustomButton';
import CustomTextInput from '../Componants/CustomInput';
import {useSelector, useDispatch} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {RootState} from '../Store/types';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../Navigation/types';
import {UpdateProfileAPI} from '../Store/Actions/AuthActions';
import {UploadProfilePictureAPI} from '../Store/Actions/AuthActions';

const {width, height} = Dimensions.get('window');

type NavProp = NativeStackNavigationProp<RootStackParamList, 'EditProfile'>;

const EditProfile = () => {
  const {userDetails} = useSelector((state: RootState) => state.auth);
  const nav = useNavigation<NavProp>();
  const [loading, setLoading] = useState(false);

  const [data, setData] = useState({
    userImage: {
      uri: '',
      path: '',
      mime: '',
      fileName: '',
      type: '',
    },
    first_name: userDetails?.first_name || '',
    last_name: userDetails?.last_name || '',
    phone: (userDetails as any)?.phone || '',
  });

  const openImagePicker = () => {
    launchImageLibrary({mediaType: 'photo', quality: 0.7}, response => {
      if (
        response &&
        !response.didCancel &&
        response.assets &&
        Array.isArray(response.assets) &&
        response.assets.length > 0
      ) {
        const asset = response.assets[0];

        setData(prev => ({
          ...prev,
          userImage: {
            uri: asset.uri || '',
            path: asset.uri || '',
            mime: asset.type || '',
            fileName: asset.fileName || 'profile.jpg',
            type: asset.type || 'image/jpeg',
          },
        }));
      } else if (response?.errorCode) {
        console.log('Image Picker Error:', response.errorMessage);
      }
    });
  };

  const handleUpdateProfile = () => {
    const payload: any = {
      first_name: data.first_name,
      last_name: data.last_name,
      phone: data.phone,
    };

    setLoading(true);

    UpdateProfileAPI(payload, setLoading, () => {
      if (data.userImage?.uri) {
        UploadProfilePictureAPI(
          {
            uri: data.userImage.uri,
            name: data.userImage.fileName,
            type: data.userImage.type,
          },
          setLoading,
          () => {
            setLoading(false);
            nav.goBack();
          },
        );
      } else {
        setLoading(false);
        nav.goBack();
      }
    });
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.cont}
      showsVerticalScrollIndicator={false}>
      <TouchableOpacity onPress={openImagePicker}>
        <Image
          source={
            data.userImage?.uri
              ? {uri: data.userImage.uri}
              : userDetails?.profile_picture
              ? {uri: userDetails.profile_picture}
              : IMAGES.profile
          }
          style={styles.profile}
        />
      </TouchableOpacity>
      <CustomTextInput
        placeholder="First Name"
        value={data.first_name}
        setValue={val => setData({...data, first_name: val})}
      />
      <CustomTextInput
        placeholder="Last Name"
        value={data.last_name}
        setValue={val => setData({...data, last_name: val})}
      />
      <CustomTextInput
        placeholder="Phone"
        keyboardType="numeric"
        value={data.phone}
        setValue={val => setData({...data, phone: val})}
      />

      <CustomButton
        auth
        loading={loading}
        title={'Update Profile'}
        onPress={handleUpdateProfile}
      />
    </ScrollView>
  );
};

export default EditProfile;

const styles = StyleSheet.create({
  profile: {
    resizeMode: 'cover',
    width: SIZES.width40,
    height: SIZES.width40,
    marginBottom: SIZES.width07,
    marginTop: SIZES.width15,
    borderRadius: (width * 0.4) / 2,
  },
  cont: {
    alignItems: 'center',
    paddingBottom: height * 0.07,
  },
  container: {
    flex: 1,
    paddingHorizontal: width * 0.05,
    backgroundColor: COLORS.primary,
  },
  name: {
    fontSize: width * 0.06,
    color: COLORS.white,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  email: {
    fontSize: width * 0.045,
    color: COLORS.white,
    marginBottom: 15,
  },
});
