import React, {useEffect, useState} from 'react';
import {FlatList, ScrollView, StyleSheet, Text} from 'react-native';
import {SIZES} from '../Constants/SIZES';
import {COLORS} from '../Constants/COLORS';
import {FONTS} from '../Constants/FONTS';
import BackButton from '../Componants/BackButton';
import {listFavAPI} from '../Store/Actions/UserActions';
import Loader from '../Componants/Loader';
import PostCards from '../Componants/PostCards';

const FavouritePostList = () => {
  const [load, setLoad] = useState(true);
  const [post, setPost] = useState<any>([]);

  useEffect(() => {
    listFavAPI(setPost, setLoad);
  }, []);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <BackButton />
      <Text style={styles.name}>FavouritePostList</Text>
      {load ? (
        <Loader />
      ) : (
        <FlatList
          data={post}
          keyExtractor={item => item.id}
          renderItem={({item}) => (
            <PostCards item={item} width={SIZES.width90} />
          )}
          scrollEnabled={false}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={() => (
            <Text style={styles.favText}>
              No post is added in your favourites
            </Text>
          )}
        />
      )}
    </ScrollView>
  );
};

export default FavouritePostList;

const styles = StyleSheet.create({
  favText: {
    color: COLORS.white,
    fontSize: SIZES.width04,
  },
  name: {
    fontSize: SIZES.width06,
    color: COLORS.white,
    fontFamily: FONTS.bold,
    marginVertical: 15,
  },
  container: {
    height: SIZES.height,
    padding: SIZES.width05,
    backgroundColor: COLORS.primary,
  },
});
