import React, {useEffect, useState} from 'react';
import {
  ScrollView,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ImageBackground,
  View,
  ActivityIndicator,
} from 'react-native';
import CustomTextInput from '../../Src/Componants/CustomInput';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {ICONS, IMAGES} from '../Constants/IMAGES';
import {COLORS} from '../Constants/COLORS';
import {SIZES} from '../Constants/SIZES';
import BackButton from '../Componants/BackButton';
import CustomButton from '../Componants/CustomButton';
import {RootStackParamList} from '../Navigation/types';
import {isValidEmail, showError} from '../Constants/FlashMessage';
import {LoginUserAPI} from '../Store/Actions/AuthActions';
import GlobalStyles from '../Constants/GlobalStyles';
import {useDispatch} from 'react-redux';
import {setFirst} from '../Store/Reducers/AuthReducer';

type SignInNavProp = NativeStackNavigationProp<RootStackParamList, 'SignIn'>;

const SignIn = () => {
  const navigation = useNavigation<SignInNavProp>();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPass, setShowPass] = useState(true);
  const [load, setLoad] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setFirst());
  }, []);

  const logFunc = async () => {
    if (!email && !password) {
      showError('Enter fields');
      return;
    }
    if (isValidEmail(email.trim().toLowerCase())) {
      showError('Enter valid email');
      return;
    }
    const data = {
      email: email.trim().toLowerCase(),
      password: password.trim(),
    };
    setLoad(true);
    await LoginUserAPI(data, setLoad);
  };

  return (
    <ImageBackground source={IMAGES.signUpBg} style={styles.bg}>
      <ScrollView contentContainerStyle={styles.inner}>
        <BackButton route={'BottomNavigation'} />
        <Text style={styles.title}>Welcome Back!</Text>
        {load ? (
          <View style={GlobalStyles.loadView}>
            <ActivityIndicator size={'large'} color={COLORS.white} />
          </View>
        ) : (
          <React.Fragment>
            <TouchableOpacity style={styles.fbBtn}>
              <Image source={ICONS.facebook} style={styles.socialIcon} />
              <Text style={styles.btnText}>CONTINUE WITH FACEBOOK</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.googleBtn,
                styles.fbBtn,
                {
                  backgroundColor: 'transparent',
                },
              ]}>
              <Image source={ICONS.Google} style={styles.socialIcon} />
              <Text style={styles.btnText}>CONTINUE WITH GOOGLE</Text>
            </TouchableOpacity>
            <Text style={styles.orText}>OR LOG IN WITH EMAIL</Text>
            <CustomTextInput
              placeholder="Email address"
              placeholderTextColor={COLORS.gray4}
              value={email}
              setValue={setEmail}
            />
            <CustomTextInput
              placeholder="Password"
              placeholderTextColor={COLORS.gray4}
              value={password}
              setValue={setPassword}
              secure={showPass}
              setSecure={setShowPass}
              rightIcon={require('../../Src/assets/Icons/eye.png')}
            />
            <View style={{marginVertical: 15}} />
            <CustomButton
              title={'LOG IN'}
              onPress={logFunc}
              auth
              bgColor={COLORS.purple}
              color={COLORS.white}
            />
            <View style={{marginVertical: 15}} />
            <TouchableOpacity
              style={{alignSelf: 'center'}}
              onPress={() => navigation.navigate('ForgotPassword')}>
              <Text style={styles.forgot}>Forgot Password?</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.signupRow}
              onPress={() => navigation.navigate('SignUp')}>
              <Text style={styles.signupText}>
                DON'T HAVE AN ACCOUNT?{' '}
                <Text style={styles.signupLink}>SIGN UP</Text>
              </Text>
            </TouchableOpacity>
          </React.Fragment>
        )}
      </ScrollView>
    </ImageBackground>
  );
};

export default SignIn;

const styles = StyleSheet.create({
  inner: {
    padding: SIZES.width05,
  },
  title: {
    fontSize: SIZES.width06,
    color: COLORS.white,
    fontWeight: 'bold',
    marginVertical: SIZES.width05,
    textAlign: 'center',
  },
  fbBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.purple2,
    padding: SIZES.width05,
    borderRadius: 100,
    width: '100%',
    justifyContent: 'center',
    marginBottom: SIZES.width03,
  },
  googleBtn: {
    borderColor: COLORS.white,
    borderWidth: 2,
    borderRadius: 100,
    width: '100%',
  },
  socialIcon: {
    width: SIZES.width05,
    height: SIZES.width05,
    resizeMode: 'contain',
    position: 'absolute',
    left: SIZES.width07,
  },
  btnText: {
    color: COLORS.white,
    fontWeight: '500',
    fontSize: SIZES.width04,
  },
  orText: {
    color: COLORS.gray4,
    marginTop: SIZES.width04,
    marginBottom: SIZES.width07,
    fontWeight: 'bold',
    fontSize: SIZES.width04,
    textAlign: 'center',
  },
  forgot: {
    color: COLORS.white,
    fontSize: SIZES.width04,
  },
  signupRow: {
    marginTop: SIZES.width10,
    alignSelf: 'center',
  },
  signupText: {
    color: COLORS.gray4,
    fontSize: SIZES.width04,
  },
  signupLink: {
    color: COLORS.purple,
    fontWeight: '600',
  },
  bg: {
    width: SIZES.width,
    height: SIZES.height,
    resizeMode: 'cover',
  },
});
