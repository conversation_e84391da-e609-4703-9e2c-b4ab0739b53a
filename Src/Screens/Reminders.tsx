import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Platform,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import {COLORS} from '../Constants/COLORS';

const {width, height} = Dimensions.get('window');

const days = ['SU', 'M', 'T', 'W', 'TH', 'F', 'S'];

const Reminders = () => {
  const [selectedDays, setSelectedDays] = useState<string[]>(['TH']);
  const [time, setTime] = useState(new Date());

  const toggleDay = (day: string) => {
    if (selectedDays.includes(day)) {
      setSelectedDays(selectedDays.filter(d => d !== day));
    } else {
      setSelectedDays([...selectedDays, day]);
    }
  };

  const onChangeTime = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || time;
    setTime(currentDate);
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.scrollContainer}>
      <Text style={styles.title}>
        What time would you like to{'\n'}Prayer & Meditation?
      </Text>
      <Text style={styles.subtitle}>
        Any time you can choose but We recommend{'\n'}first thing in the
        morning.
      </Text>

      <View style={styles.pickerWrapper}>
        {Platform.OS === 'ios' ? (
          <View style={{height: height * 0.25}}>
            <DateTimePicker
              value={time}
              mode="time"
              display="spinner"
              onChange={onChangeTime}
              themeVariant="light"
            />
          </View>
        ) : (
          <TouchableOpacity style={styles.androidTimePlaceholder}>
            <Text style={styles.androidTimeText}>
              {time.toLocaleTimeString()}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      <Text style={[styles.title, {marginTop: 30}]}>
        Which day would you like to{'\n'}Prayer & Meditation?
      </Text>
      <Text style={styles.subtitle}>
        Everyday is best, but we recommend picking{'\n'}at least five.
      </Text>

      <View style={styles.daysWrapper}>
        {days.map(day => {
          const isSelected = selectedDays.includes(day);
          return (
            <TouchableOpacity
              key={day}
              style={[
                styles.dayCircle,
                isSelected ? styles.selectedDay : styles.unselectedDay,
              ]}
              onPress={() => toggleDay(day)}>
              <Text
                style={[
                  styles.dayText,
                  isSelected
                    ? {color: COLORS.white}
                    : {color: COLORS.white, opacity: 0.6},
                ]}>
                {day}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>

      <TouchableOpacity style={styles.saveButton}>
        <Text style={styles.saveText}>SAVE</Text>
      </TouchableOpacity>

      {/* <TouchableOpacity>
        <Text style={styles.noThanks}>NO THANKS</Text>
      </TouchableOpacity> */}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.primary,
  },
  scrollContainer: {
    paddingTop: height * 0.08,
    paddingHorizontal: width * 0.05,
    paddingBottom: height * 0.05,
  },
  title: {
    color: COLORS.white,
    fontSize: width * 0.065,
    fontWeight: '600',
    marginBottom: height * 0.01,
    lineHeight: width * 0.08,
  },
  subtitle: {
    color: COLORS.gray4,
    fontSize: width * 0.044,
    marginBottom: height * 0.025,
    lineHeight: width * 0.065,
  },
  pickerWrapper: {
    backgroundColor: COLORS.white,
    borderRadius: width * 0.06,
    overflow: 'hidden',
    marginBottom: height * 0.04,
    marginTop: height * 0.02,
  },
  androidTimePlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
    height: height * 0.12,
  },
  androidTimeText: {
    fontSize: width * 0.045,
    fontWeight: '600',
    color: COLORS.black,
  },
  daysWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: height * 0.025,
    marginBottom: height * 0.035,
  },
  dayCircle: {
    width: width * 0.12,
    height: width * 0.12,
    borderRadius: width * 0.06,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedDay: {
    backgroundColor: COLORS.purple,
  },
  unselectedDay: {
    borderWidth: 1,
    borderColor: COLORS.white,
    backgroundColor: 'transparent',
  },
  dayText: {
    fontSize: width * 0.035,
    fontWeight: '600',
  },
  saveButton: {
    backgroundColor: COLORS.purple,
    paddingVertical: height * 0.028,
    borderRadius: width * 0.08,
    alignItems: 'center',
    marginBottom: height * 0.025,
    marginTop: height * 0.015,
  },
  saveText: {
    color: COLORS.white,
    fontSize: width * 0.04,
    fontWeight: '600',
  },
  noThanks: {
    color: COLORS.white,
    fontSize: width * 0.035,
    textAlign: 'center',
  },
});

export default Reminders;
