import React, {useEffect, useState} from 'react';
import {
  ImageBackground,
  Text,
  View,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import {ICONS, IMAGES} from '../Constants/IMAGES';
import {COLORS} from '../Constants/COLORS';
import {Dimensions} from 'react-native';
import BackButton from '../Componants/BackButton';
import {RouteProp} from '@react-navigation/native';
import {RootStackParamList} from '../Navigation/types';
import {CategoriesDeatilAPI} from '../Store/Actions/UserActions';
import Loader from '../Componants/Loader';
import PostCard from '../Componants/PostCard';

const {width, height} = Dimensions.get('window');

type CourseDetailRouteProp = RouteProp<RootStackParamList, 'CourseDetail'>;

type Props = {
  route: CourseDetailRouteProp;
};

const CourseDetail: React.FC<Props> = ({route}) => {
  const {
    params: {id},
  } = route;
  const [data, setData] = useState<any>([]);
  const [load, setLoad] = useState(true);

  const getDetails = () => {
    CategoriesDeatilAPI(id, setData, setLoad);
  };

  useEffect(() => {
    getDetails();
  }, [id]);

  console.log('data', data);

  return (
    <ScrollView style={styles.container}>
      {load ? (
        <Loader />
      ) : (
        <React.Fragment>
          <ImageBackground source={IMAGES.Imagebg} style={styles.imagebg}>
            <View style={styles.topIcons}>
              <BackButton />
              {/* <View style={styles.rightIcons}>
                <TouchableOpacity>
                  <Image source={ICONS.heart2} style={styles.topIcon} />
                </TouchableOpacity>
                <TouchableOpacity>
                  <Image source={ICONS.download} style={styles.topIcon} />
                </TouchableOpacity>
              </View> */}
            </View>
          </ImageBackground>

          {/* <View> */}
          <Text style={styles.heading}>{data?.name}</Text>
          {/* <Text style={styles.subheading}>COURSE</Text> */}
          {/* <Text style={styles.subheading2}>
              Ease the mind into a restful night’s sleep with these deep,
              ambient tones.
            </Text> */}
          {/* </View> */}

          {/* <View style={styles.row}>
            <View style={styles.iconRow}>
              <Image source={ICONS.heart} style={styles.iconImage} />
              <Text style={styles.text}>24,234 Favorites</Text>
            </View>
            <View style={styles.iconRow}>
              <Image source={ICONS.headphones} style={styles.iconImage} />
              <Text style={styles.text}>34,234 Listening</Text>
            </View>
          </View> */}

          {/* <Text style={styles.subheading3}>Pick a Narrator</Text>
          <View style={styles.row2}>
            <Text style={[styles.text1, styles.activeTab]}>MALE VOICE</Text>
            <Text style={styles.text1}>FEMALE VOICE</Text>
          </View> */}

          <View style={styles.divider} />
          {data?.posts?.length > 0 && (
            <>
              <Text style={styles.subheading3}>Posts</Text>
              {data?.posts?.map((val: any, ind: number) => {
                return <PostCard item={val} key={ind} />;
              })}
            </>
          )}
          {/* <View style={styles.audioItem}>
            <Image source={ICONS.play2} style={styles.audioIcon1} />
            <View>
              <Text style={styles.audioTitle}>Morning Psalms</Text>
              <Text style={styles.audioTime}>10 MIN</Text>
            </View>
          </View>
          <View style={styles.dividerLine} /> */}
        </React.Fragment>
      )}
    </ScrollView>
  );
};

export default CourseDetail;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.blue2,
  },
  imagebg: {
    width: width,
    height: height * 0.35,
    resizeMode: 'cover',
    justifyContent: 'space-between',
  },
  topIcons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: width * 0.05,
    alignItems: 'center',
  },
  // rightIcons: {
  //   flexDirection: 'row',
  //   gap: width * 0.04,
  // },
  // topIcon: {
  //   width: width * 0.11,
  //   height: width * 0.11,
  //   resizeMode: 'contain',
  //   borderWidth: 1,
  //   borderRadius: width * 0.15,
  //   borderColor: COLORS.white,
  //   padding: width * 0.015,
  //   backgroundColor: COLORS.black50,
  // },
  heading: {
    fontWeight: '400',
    fontSize: width * 0.075,
    color: COLORS.white,
    paddingHorizontal: width * 0.04,
    marginTop: height * 0.04,
  },
  // subheading: {
  //   color: COLORS.gray4,
  //   paddingHorizontal: width * 0.05,
  //   marginTop: height * 0.015,
  //   fontSize: width * 0.045,
  // },
  // subheading2: {
  //   color: COLORS.gray4,
  //   paddingHorizontal: width * 0.05,
  //   marginTop: height * 0.025,
  //   fontSize: width * 0.046,
  // },
  // row: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-evenly',
  //   marginTop: height * 0.025,
  // },
  // iconRow: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  // },
  // iconImage: {
  //   width: width * 0.05,
  //   height: width * 0.05,
  //   resizeMode: 'contain',
  //   marginRight: width * 0.015,
  // },
  // text: {
  //   color: COLORS.white,
  //   fontSize: width * 0.045,
  // },
  subheading3: {
    color: COLORS.white,
    marginTop: height * 0.02,
    paddingHorizontal: width * 0.05,
    fontWeight: 'bold',
    fontSize: width * 0.06,
  },
  // row2: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-around',
  //   marginTop: height * 0.02,
  // },
  // text1: {
  //   color: COLORS.gray4,
  //   fontSize: width * 0.045,
  // },
  // activeTab: {
  //   borderBottomWidth: 2,
  //   borderColor: COLORS.white,
  //   paddingBottom: height * 0.005,
  // },
  divider: {
    height: 1,
    backgroundColor: COLORS.gray4,
    marginVertical: height * 0.025,
  },
  // dividerLine: {
  //   height: 1,
  //   backgroundColor: COLORS.gray4,
  //   marginLeft: width * 0.2,
  //   marginRight: width * 0.05,
  //   marginBottom: height * 0.012,
  // },
  // audioItem: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   gap: width * 0.03,
  //   paddingHorizontal: width * 0.05,
  //   marginBottom: height * 0.025,
  // },
  // audioTitle: {
  //   color: COLORS.white,
  //   fontSize: width * 0.045,
  // },
  // audioTime: {
  //   color: COLORS.gray4,
  //   fontSize: width * 0.035,
  // },
  // audioIcon1: {
  //   width: width * 0.11,
  //   height: width * 0.11,
  //   resizeMode: 'contain',
  //   borderWidth: 1,
  //   borderRadius: width * 0.15,
  //   borderColor: COLORS.purple,
  //   padding: width * 0.025,
  //   backgroundColor: COLORS.purple,
  // },
});
