import React, {useEffect, useRef, useState} from 'react';
import {RouteProp} from '@react-navigation/native';
import {
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewToken,
} from 'react-native';
import {RootStackParamList} from '../Navigation/types';
import {addFavAPI, featuredPostDetailAPI} from '../Store/Actions/UserActions';
import Loader from '../Componants/Loader';
import {COLORS} from '../Constants/COLORS';
import {SIZES} from '../Constants/SIZES';
import BackButton from '../Componants/BackButton';
import {FONTS} from '../Constants/FONTS';
import ImageComp from '../Componants/ImageComp';
import AudioComp from '../Componants/AudioComp';
import {ICONS} from '../Constants/IMAGES';

type PostDetailRouteProp = RouteProp<RootStackParamList, 'PostDetail'>;

type Props = {
  route: PostDetailRouteProp;
};

const PostDetail: React.FC<Props> = ({route}) => {
  const {
    params: {id},
  } = route;
  const [detail, setDetail] = useState<any>();
  const [load, setLoad] = useState(true);
  const [activeIndex, setActiveIndex] = useState(0);
  const viewabilityConfig = useRef({viewAreaCoveragePercentThreshold: 50});
  const onViewableItemsChanged = useRef(
    ({viewableItems}: {viewableItems: ViewToken[]}) => {
      if (viewableItems.length > 0) {
        setActiveIndex(viewableItems[0].index || 0);
      }
    },
  );

  const addFav = () => {
    setLoad(true);
    addFavAPI(id, setLoad);
  };

  useEffect(() => {
    featuredPostDetailAPI(id, setDetail, setLoad);
  }, []);

  return (
    <ScrollView showsHorizontalScrollIndicator={false} style={styles.cont}>
      {load ? (
        <Loader />
      ) : (
        <>
          <View style={styles.iconView}>
            <BackButton />
            <TouchableOpacity style={styles.likeView} onPress={addFav}>
              <Image source={ICONS.heart2} style={styles.topIcon} />
            </TouchableOpacity>
          </View>
          <FlatList
            data={detail?.images}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            keyExtractor={(_, index) => index.toString()}
            renderItem={({item}) => <ImageComp item={item} />}
            onViewableItemsChanged={onViewableItemsChanged.current}
            viewabilityConfig={viewabilityConfig.current}
          />
          <View style={styles.indicatorContainer}>
            {detail?.images.map((_: any, index: number) => (
              <View
                key={index}
                style={[
                  styles.indicator,
                  index === activeIndex && styles.activeIndicator,
                ]}
              />
            ))}
          </View>
          <View style={styles.inner}>
            <Text style={styles.title}>{detail?.title}</Text>
            <Text style={styles.desc}>{detail?.description}</Text>
            {detail?.audio && <AudioComp item={detail?.audio} />}
          </View>
        </>
      )}
    </ScrollView>
  );
};

export default PostDetail;

const styles = StyleSheet.create({
  iconView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    margin: 10,
  },
  topIcon: {
    resizeMode: 'contain',
    width: SIZES.width07,
    height: SIZES.width07,
  },
  likeView: {
    padding: 10,
    borderWidth: 1,
    borderColor: COLORS.white,
    borderRadius: 100,
  },
  desc: {
    color: COLORS.white,
    fontSize: SIZES.width045,
    marginVertical: 10,
  },
  title: {
    fontSize: SIZES.width06,
    color: COLORS.white,
    fontFamily: FONTS.bold,
  },
  inner: {
    padding: SIZES.width05,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.gray4,
    margin: 4,
  },
  activeIndicator: {
    backgroundColor: COLORS.white,
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 8,
  },
  cont: {
    backgroundColor: COLORS.primary,
    height: SIZES.height,
  },
});
