import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  ImageBackground,
  ActivityIndicator,
} from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';
import Slider from '@react-native-community/slider';
import { ICONS, IMAGES } from '../Constants/IMAGES';
import { COLORS } from '../Constants/COLORS';
import { musicByIdAPI } from '../Store/Actions/UserActions';
import BackButton from '../Componants/BackButton';
import SoundPlayer from 'react-native-sound-player';

const { width, height } = Dimensions.get('window');

type RootStackParamList = {
  Music: { id: number };
};
type MusicScreenRouteProp = RouteProp<RootStackParamList, 'Music'>;

const Music = () => {
  const route = useRoute<MusicScreenRouteProp>();
  const [musicData, setMusicData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentPosition, setCurrentPosition] = useState(0);

  useEffect(() => {
    const id = route?.params?.id || 2;
    setLoading(true);
    musicByIdAPI(id, setMusicData, setLoading);
  }, []);

  const audioUrl = musicData?.audio
    ? `https://api.lightofthewords.com/uploads/music/${musicData?.audio}`
    : '';

  useEffect(() => {
    const interval = setInterval(() => {
      try {
        SoundPlayer.getInfo().then(info => {
          setDuration(info.duration || 0);
          setCurrentPosition(info.currentTime || 0);
        });
      } catch (e) {
        console.log('Error getting sound info:', e);
      }
    }, 500);
    return () => clearInterval(interval);
  }, []);

  const handlePlayPause = () => {
    if (!audioUrl) return;
    if (isPlaying) {
      SoundPlayer.pause();
      setIsPlaying(false);
    } else {
      try {
        SoundPlayer.playUrl(audioUrl);
        setIsPlaying(true);
      } catch (e) {
        console.log('Error playing sound:', e);
      }
    }
  };

  const handleRewind = () => {
    SoundPlayer.getInfo().then(info => {
      const newTime = Math.max(0, info.currentTime - 15);
      SoundPlayer.seek(newTime);
    });
  };

  const handleForward = () => {
    SoundPlayer.getInfo().then(info => {
      const newTime = Math.min(info.duration, info.currentTime + 15);
      SoundPlayer.seek(newTime);
    });
  };

  const onSlide = (value: number) => {
    SoundPlayer.seek(value * duration);
  };

  const imageUrl = musicData?.image
    ? `https://api.lightofthewords.com/uploads/music/${musicData?.image}`
    : '';

  const formatTime = (sec: number) => {
    const minutes = Math.floor(sec / 60);
    const seconds = Math.floor(sec % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  return (
    <ImageBackground
      source={IMAGES.Musicbg3}
      style={styles.container}
      resizeMode="cover">
      <View style={styles.topIcons}>
        <BackButton />
        <View style={styles.rightIcons}>
          <TouchableOpacity style={styles.iconButton}>
            <Image source={ICONS.heart} style={styles.icon} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton}>
            <Image source={ICONS.download} style={styles.icon} />
          </TouchableOpacity>
        </View>
      </View>

      {loading ? (
        <ActivityIndicator
          size="large"
          color={COLORS.white}
          style={{ marginTop: height * 0.2 }}
        />
      ) : (
        <>
        
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: imageUrl }}
              style={styles.centerImage}
              resizeMode="cover"
            />
          </View>

          <Text style={styles.title}>{musicData?.title || ''}</Text>
          <Text style={styles.subtitle}>{musicData?.description || ''}</Text>

          <View style={styles.controls}>
            <TouchableOpacity onPress={handleRewind}>
              <Image source={ICONS.backward} style={styles.controlIcon} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.playButton}
              onPress={handlePlayPause}>
              <Image
                source={isPlaying ? ICONS.pause : ICONS.play2}
                style={styles.playIcon}
              />
            </TouchableOpacity>

            <TouchableOpacity onPress={handleForward}>
              <Image source={ICONS.forward} style={styles.controlIcon} />
            </TouchableOpacity>
          </View>

          <View style={styles.progressContainer}>
            <Slider
              style={styles.slider}
              minimumValue={0}
              maximumValue={1}
              minimumTrackTintColor={COLORS.purple2}
              maximumTrackTintColor={COLORS.gray7}
              thumbTintColor={COLORS.purple2}
              value={duration ? currentPosition / duration : 0}
              onSlidingComplete={onSlide}
            />
            <View style={styles.timeRow}>
              <Text style={styles.time}>{formatTime(currentPosition)}</Text>
              <Text style={styles.time}>{formatTime(duration)}</Text>
            </View>
          </View>
        </>
      )}
    </ImageBackground>
  );
};

export default Music;


const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: height * 0.06,
    paddingHorizontal: width * 0.05,
  },
  topIcons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  iconButton: {
    backgroundColor: COLORS.purple2,
    padding: width * 0.03,
    borderRadius: width * 0.06,
  },
  icon: {
    width: width * 0.06,
    height: width * 0.06,
    tintColor: COLORS.white,
  },
  rightIcons: {
    flexDirection: 'row',
    gap: width * 0.03,
  },
  plusIcon: {
    width: width * 0.7,
    height: width * 0.7,
    alignSelf: 'center',
    resizeMode: 'contain',
    marginTop: height * 0.05,
    marginBottom: height * 0.04,
  },
  title: {
    color: COLORS.white,
    fontSize: width * 0.09,
    fontWeight: '400',
    textAlign: 'center',
    marginTop: height * 0.05,
  },
  subtitle: {
    color: COLORS.gray5,
    fontSize: width * 0.035,
    textAlign: 'center',
    marginTop: height * 0.008,
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    marginTop: height * 0.06,
    marginHorizontal: width * 0.12,
  },
  controlIcon: {
    width: width * 0.09,
    height: width * 0.09,
    tintColor: COLORS.gray5,
  },
  playButton: {
    backgroundColor: COLORS.purple2,
    padding: width * 0.08,
    borderRadius: width * 0.12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  playIcon: {
    width: width * 0.07,
    height: width * 0.08,
    tintColor: COLORS.white,
  },
  progressContainer: {
    marginTop: height * 0.04,
    paddingHorizontal: width * 0.05,
  },
  slider: {
    width: '100%',
    height: height * 0.04,
  },
  timeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: -height * 0.01,
  },
  time: {
    color: COLORS.gray7,
    fontSize: width * 0.04,
  },
    imageContainer: {
    width: '100%',
    alignItems: 'center',
    marginTop: height * 0.03, 
  },
  centerImage: {
    width: width * 0.6,
    height: width * 0.6,
    borderRadius: (width * 0.6) * 0.1,
  },
});
