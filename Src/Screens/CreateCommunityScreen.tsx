import React, {useEffect, useState} from 'react';
import {
  Dimensions,
  Image,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import {
  launchImageLibrary,
  ImageLibraryOptions,
} from 'react-native-image-picker';
import DropDownPicker from 'react-native-dropdown-picker';
import {COLORS} from '../Constants/COLORS';
import CustomTextInput from '../Componants/CustomInput';
import BackButton from '../Componants/BackButton';
import Loader from '../Componants/Loader';
import {
  CommunityCategoriesAPI,
  CommunityReasonAPI,
  CreateCommunityAPI,
} from '../Store/Actions/CommunityActions';
import CustomButton from '../Componants/CustomButton';
import {showError} from '../Constants/FlashMessage';
import {useSelector} from 'react-redux';
import {RootState} from '../Store/types';
import {useNavigation} from '@react-navigation/native';

const {width, height} = Dimensions.get('window');

const CreateCommunityScreen = () => {
  const nav = useNavigation();
  const {userDetails} = useSelector((state: RootState) => state.auth);
  const [load, setLoad] = useState(true);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [image, setImage] = useState<any>(null);

  const [categoryOpen, setCategoryOpen] = useState(false);
  const [categoryValue, setCategoryValue] = useState(null);
  const [categoryItems, setCategoryItems] = useState<any>([]);

  const [reasonOpen, setReasonOpen] = useState(false);
  const [reasonValue, setReasonValue] = useState<any>([]);
  const [reasonItems, setReasonItems] = useState<any>([]);

  const pickImage = () => {
    const options: ImageLibraryOptions = {
      mediaType: 'photo',
      quality: 0.7,
    };

    launchImageLibrary(options, response => {
      if (response.didCancel) {
        console.log('User ne cancel kar diya');
      } else if (response.errorCode) {
        console.log('Error: ', response.errorMessage);
      } else if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];
        setImage({
          uri: asset?.uri,
          fileName: asset.fileName,
          type: asset.type,
        });
      }
    });
  };

  const submit = async () => {
    if (!name || !description || !image || !categoryValue || !reasonValue) {
      showError('Enter complete details');
      return;
    }
    setLoad(true);
    const formData = new FormData();
    formData.append('name', name);
    formData.append('description', description);
    formData.append('image', {
      uri: Platform.OS === 'ios' ? image.uri.replace('file://', '') : image.uri,
      type: image.type || 'image/jpeg',
      name: image.fileName || `image_${Date.now()}.jpg`,
    } as any);
    formData.append('created_by', userDetails.id);
    reasonValue.forEach((element: string) => {
      formData.append('reason_ids[]', element);
    });
    formData.append('community_category_id', categoryValue);
    await CreateCommunityAPI(formData, nav);
  };

  useEffect(() => {
    Promise.all([
      CommunityCategoriesAPI(setCategoryItems),
      CommunityReasonAPI(setReasonItems),
    ]).finally(() => setLoad(false));
  }, []);

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.scrollContainer}>
      {load ? (
        <Loader />
      ) : (
        <>
          <BackButton />
          <Text style={styles.titleMain}>Create Community</Text>

          <TouchableOpacity onPress={pickImage} style={styles.imagePicker}>
            {image ? (
              <Image
                source={{uri: image.uri}}
                style={styles.imageButtonImage}
                resizeMode="cover"
              />
            ) : (
              <Text style={styles.imagePickerText}>Choose Image</Text>
            )}
          </TouchableOpacity>

          <CustomTextInput
            value={name}
            setValue={setName}
            placeholder={'Community Name'}
            placeholderTextColor={COLORS.gray5}
          />
          <CustomTextInput
            value={description}
            setValue={setDescription}
            placeholder={'Description'}
            placeholderTextColor={COLORS.gray5}
          />

          <DropDownPicker
            open={categoryOpen}
            value={categoryValue}
            items={categoryItems}
            setOpen={setCategoryOpen}
            setValue={setCategoryValue}
            placeholder={'Select Community Category'}
            style={{
              backgroundColor: COLORS.gray,
              marginVertical: 5,
              borderRadius: 15,
            }}
            dropDownContainerStyle={{backgroundColor: COLORS.gray}}
            textStyle={{color: COLORS.gray5}}
            zIndex={2000}
            zIndexInverse={1000}
          />
          <DropDownPicker
            multiple={true}
            min={0}
            max={5}
            open={reasonOpen}
            value={reasonValue}
            items={reasonItems}
            setOpen={setReasonOpen}
            setValue={setReasonValue}
            placeholder={'Select Reasons'}
            style={{
              backgroundColor: COLORS.gray,
              marginVertical: 5,
              borderRadius: 15,
            }}
            dropDownContainerStyle={{backgroundColor: COLORS.gray}}
            textStyle={{color: COLORS.gray5}}
            zIndex={1000}
            zIndexInverse={2000}
          />
          <CustomButton auth title={'Create'} onPress={submit} />
        </>
      )}
    </ScrollView>
  );
};

export default CreateCommunityScreen;

const styles = StyleSheet.create({
  titleMain: {
    color: COLORS.white,
    fontSize: width * 0.065,
    fontWeight: '600',
    marginVertical: height * 0.02,
    lineHeight: width * 0.08,
  },
  scrollContainer: {
    paddingVertical: height * 0.02,
    paddingHorizontal: width * 0.05,
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.primary,
  },
  imagePicker: {
    marginTop: height * 0.02,
    width: width * 0.9,
    height: height * 0.2,
    backgroundColor: COLORS.gray,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
    marginBottom: 5,
  },
  imagePickerText: {
    color: COLORS.gray5,
    fontSize: width * 0.04,
    fontWeight: '500',
  },
  imageButtonImage: {
    width: '100%',
    height: '100%',
  },
});
