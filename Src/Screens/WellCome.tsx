import React from 'react';
import {View, Text, StyleSheet, Image} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {IMAGES} from '../Constants/IMAGES';
import {COLORS} from '../Constants/COLORS';
import {SIZES} from '../Constants/SIZES';
import CustomButton from '../Componants/CustomButton';
import {RootStackParamList} from '../Navigation/types';

const Welcome = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  return (
    <View style={styles.container}>
      <Image source={IMAGES.sameemlogo} style={styles.logo} />
      <Text style={styles.heading}>
        Hi User, Welcome{'\n'}Light of the words
      </Text>
      <Text style={styles.subText}>
        Explore the app, Find some peace of mind to{'\n'}
        prepare for Prayer & Meditation.
      </Text>
      <Image source={IMAGES.Group6859} style={styles.illustration} />
      <CustomButton
        auth
        title={'GET STARTED'}
        onPress={() => navigation.navigate('Welcome2')}
      />
    </View>
  );
};

export default Welcome;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.primary,
    height: SIZES.height,
  },
  logo: {
    width: SIZES.width70,
    height: SIZES.height25,
    resizeMode: 'contain',
    marginVertical: SIZES.width05,
    alignSelf: 'center',
  },
  heading: {
    color: COLORS.peach2,
    fontSize: SIZES.width07,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  subText: {
    color: COLORS.white4,
    fontSize: SIZES.width045,
    textAlign: 'center',
    marginTop: SIZES.width03,
  },
  illustration: {
    width: SIZES.width,
    height: SIZES.height30,
    marginTop: 25,
    resizeMode: 'contain',
  },
});
