import React, {useEffect, useState} from 'react';
import {
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Image,
  ImageBackground,
} from 'react-native';
import CustomTextInput from '../../Src/Componants/CustomInput';
import {useNavigation} from '@react-navigation/native';
import {ICONS, IMAGES} from '../Constants/IMAGES';
import {COLORS} from '../Constants/COLORS';
import BackButton from '../Componants/BackButton';
import {SIZES} from '../Constants/SIZES';
import {FONTS} from '../Constants/FONTS';
import CustomButton from '../Componants/CustomButton';
import {isValidEmail, showError} from '../Constants/FlashMessage';
import {SignUpUserAPI} from '../Store/Actions/AuthActions';
import Loader from '../Componants/Loader';
import { useDispatch } from 'react-redux';
import { setFirst } from '../Store/Reducers/AuthReducer';

const SignUp = () => {
  const navigation = useNavigation<any>();
  const [name, setName] = useState('');
  const [Lname, setLName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [showPass, setShowPass] = useState(true);
  const [load, setLoad] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setFirst());
  }, []);

  const signUpFunc = async () => {
    if (!email && !password) {
      showError('Enter fields');
      return;
    }
    if (isValidEmail(email.trim().toLowerCase())) {
      showError('Enter valid email');
      return;
    }
    const data = {
      first_name: name,
      last_name: Lname,
      email: email.trim().toLowerCase(),
      phone: phone,
      password: password,
    };
    setLoad(true);
    await SignUpUserAPI(data, setLoad, navigation);
  };

  return (
    <ImageBackground source={IMAGES.signUpBg} style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        scrollEnabled
        contentContainerStyle={styles.cont}>
        <BackButton />
        <Text style={styles.title}>Create your account</Text>
        {load ? (
          <Loader />
        ) : (
          <React.Fragment>
            <TouchableOpacity style={styles.fbBtn}>
              <Image source={ICONS.facebook} style={styles.socialIcon} />
              <Text style={styles.btnText}>CONTINUE WITH FACEBOOK</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.googleBtn,
                styles.fbBtn,
                {
                  backgroundColor: 'transparent',
                },
              ]}>
              <Image source={ICONS.Google} style={styles.socialIcon} />
              <Text style={styles.btnText}>CONTINUE WITH GOOGLE</Text>
            </TouchableOpacity>
            <Text style={styles.orText}>OR LOG IN WITH EMAIL</Text>
            <CustomTextInput
              placeholder={'First Name'}
              placeholderTextColor={COLORS.gray4}
              value={name}
              setValue={setName}
              secure={false}
            />
            <CustomTextInput
              placeholder={'Last Name'}
              placeholderTextColor={COLORS.gray4}
              value={Lname}
              setValue={setLName}
              secure={false}
            />
            <CustomTextInput
              placeholder={'phone number'}
              placeholderTextColor={COLORS.gray4}
              keyboardType={'phone-pad'}
              value={phone}
              setValue={setPhone}
              secure={false}
            />
            <CustomTextInput
              placeholder={'Email address'}
              placeholderTextColor={COLORS.gray4}
              keyboardType="email-address"
              autoCapitalize="none"
              value={email}
              setValue={setEmail}
              secure={false}
            />
            <CustomTextInput
              placeholder={'Password'}
              placeholderTextColor={COLORS.gray4}
              secure={showPass}
              setSecure={setShowPass}
              value={password}
              setValue={setPassword}
              rightIcon={require('../../Src/assets/Icons/eye.png')}
            />
            <Text style={styles.agreeText}>
              I have read the{' '}
              <Text style={styles.linkText}>Privacy Policy</Text>
            </Text>
            <CustomButton
              title={'Sign Up'}
              auth
              bgColor={COLORS.purple}
              color={COLORS.white}
              onPress={signUpFunc}
            />
          </React.Fragment>
        )}
      </ScrollView>
    </ImageBackground>
  );
};

export default SignUp;

const styles = StyleSheet.create({
  cont: {
    paddingBottom: SIZES.height10,
  },
  container: {
    height: SIZES.height,
    width: SIZES.width,
    resizeMode: 'cover',
    padding: SIZES.width05,
  },
  title: {
    fontSize: SIZES.width06,
    color: COLORS.white,
    fontFamily: FONTS.bold,
    marginVertical: SIZES.width05,
    textAlign: 'center',
  },
  fbBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.purple2,
    padding: SIZES.width05,
    borderRadius: 100,
    width: '100%',
    justifyContent: 'center',
    marginBottom: SIZES.width03,
  },
  googleBtn: {
    borderColor: COLORS.white,
    borderWidth: 2,
    borderRadius: 100,
    width: '100%',
  },
  socialIcon: {
    width: SIZES.width05,
    height: SIZES.width05,
    resizeMode: 'contain',
    position: 'absolute',
    left: SIZES.width07,
  },
  btnText: {
    color: COLORS.white,
    fontWeight: '500',
    fontSize: SIZES.width04,
  },
  orText: {
    color: COLORS.gray4,
    marginVertical: SIZES.width04,
    fontWeight: 'bold',
    fontSize: SIZES.width04,
    textAlign: 'center',
  },
  agreeText: {
    color: COLORS.gray4,
    fontSize: SIZES.width04,
    marginTop: SIZES.width03,
    marginBottom: SIZES.width07,
  },
  linkText: {
    color: COLORS.purple2,
  },
});
