import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Dimensions,
  ScrollView,
  ImageBackground,
} from 'react-native';
import {COLORS} from '../Constants/COLORS';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {getMusicCategories, getMusicCategoryById} from '../Store/Actions/UserActions';
import {useNavigation} from '@react-navigation/native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import {StackNavigationProp} from '@react-navigation/stack';

const {width, height} = Dimensions.get('window');

const MUSIC_BASE = 'https://api.lightofthewords.com/uploads/music/';
const MUSIC_CAT_BASE = 'https://api.lightofthewords.com/uploads/music-categories/';

export type RootStackParamList = {
  Home: undefined;
  Music: { audio: string };
};

interface MusicCategory {
  id: number;
  name: string;
  image: string;
  musics: any[];
}

interface Music {
  id: number;
  title: string;
  description: string;
  image: string;
  audio: string;
  category?: {
    image: string;
  };
}

const Prayer = () => {
  const navigation = useNavigation<StackNavigationProp<RootStackParamList, 'Music'>>();
  const [musicCategories, setMusicCategories] = useState<MusicCategory[]>([]);
  const [sel, setSel] = useState<number | null>(null);
  const [musics, setMusics] = useState<Music[]>([]);

  useEffect(() => {
    const fetchCategoriesSafely = async () => {
      const token = await AsyncStorage.getItem('token');
      if (token) {
        getMusicCategories(setMusicCategories, id => {
          setSel(id);
          getMusicCategoryById(id, setMusics);
        });
      }
    };
    fetchCategoriesSafely();
  }, []);

  const handleCategoryPress = (id: number) => {
    setSel(id);
    getMusicCategoryById(id, setMusics);
  };

  const renderCategoryItem = ({item}: {item: MusicCategory}) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => handleCategoryPress(item.id)}>
      <View
        style={[
          styles.iconWrapper,
          {backgroundColor: sel === item.id ? COLORS.purple : 'transparent'},
        ]}>
        <Image
          source={{uri: `${MUSIC_CAT_BASE}${item.image}`}}
          style={styles.iconImage}
        />
      </View>
      <Text style={styles.categoryText}>{item.name}</Text>
    </TouchableOpacity>
  );

  const renderMusicCard = ({item}: {item: Music}) => {
    const imageUrl = `${MUSIC_BASE}${item.image}`;
    const categoryImgUrl = item.category?.image
      ? `${MUSIC_CAT_BASE}${item.category.image}`
      : null;

    return (
      <TouchableOpacity
        onPress={() =>
          navigation.navigate('Music', {
            id: item.id,
            audio: `${MUSIC_BASE}${item.audio}`,
          })
        }
        activeOpacity={0.8}>
        <View style={styles.card}>
          <ImageBackground
            source={{uri: imageUrl}}
            style={styles.cardImage}
            imageStyle={{borderRadius: 15}}>
            <View style={styles.overlay}>
              <Text style={styles.cardText}>{item.title}</Text>
              <Text
                style={[styles.cardText, {fontSize: 20, marginTop: 4}]}
                numberOfLines={2}>
                {item.description}
              </Text>
            </View>
          </ImageBackground>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Text style={styles.heading}>Prayer & Meditation</Text>
      <Text style={styles.subheading}>
        We can learn how to recognize when our minds are doing their normal everyday!
      </Text>

      <FlatList
  data={musicCategories.filter(i => i && i.id && i.image)}
        horizontal
        keyExtractor={item => item.id.toString()}
        renderItem={renderCategoryItem}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoryList}
      />

      <FlatList
        data={musics}
        keyExtractor={item => item.id.toString()}
        renderItem={renderMusicCard}
        numColumns={2}
        scrollEnabled={true}
        columnWrapperStyle={{justifyContent: 'space-between'}}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.primary,
    paddingTop: height * 0.04,
    paddingHorizontal: width * 0.05,
  },
  heading: {
    color: COLORS.white,
    fontSize: width * 0.075,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  subheading: {
    color: COLORS.gray5,
    fontSize: width * 0.042,
    textAlign: 'center',
    marginTop: height * 0.012,
    marginBottom: height * 0.025,
  },
  categoryList: {
    marginBottom: height * 0.025,
  },
  categoryItem: {
    alignItems: 'center',
    marginRight: width * 0.07,
  },
  iconWrapper: {
    // padding: width * 0.04,
    borderRadius: width * 0.04,
    marginBottom: height * 0.008,
    marginTop: height * 0.012,
    borderColor: COLORS.gray6,
    borderWidth: 1,
  },
  iconImage: {
    width: width * 0.11,
    height: width * 0.11,
    resizeMode: 'contain',
    borderRadius: width * 0.06,
  },
  categoryText: {
    color: COLORS.white,
    fontSize: width * 0.03,
  },
  card: {
    width: (width - width * 0.15) / 2,
    height: height * 0.25,
    marginBottom: height * 0.025,
    borderRadius: width * 0.04,
    overflow: 'hidden',
    marginTop: height * 0.04,
  },
  cardImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'flex-end',
  },
  overlay: {
    padding: width * 0.04,
    borderBottomLeftRadius: width * 0.04,
    borderBottomRightRadius: width * 0.04,
  },
  cardText: {
    color: COLORS.black,
    fontWeight: 'bold',
    fontSize: width * 0.045,
  },
});

export default Prayer;
