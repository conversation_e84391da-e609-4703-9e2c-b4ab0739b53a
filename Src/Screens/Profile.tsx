import React, {useState} from 'react';
import {Text, ScrollView, StyleSheet, Image, View} from 'react-native';
import {COLORS} from '../Constants/COLORS';
import {IMAGES} from '../Constants/IMAGES';
import {SIZES} from '../Constants/SIZES';
import {FONTS} from '../Constants/FONTS';
import CustomButton from '../Componants/CustomButton';
import {useNavigation} from '@react-navigation/native';
import {RootStackParamList} from '../Navigation/types';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useDispatch, useSelector} from 'react-redux';
import {RootState} from '../Store/types';
import {logOut} from '../Store/Reducers/AuthReducer';

type NavProp = NativeStackNavigationProp<RootStackParamList, 'Profile'>;

const Profile = () => {
  const {loginUser, userDetails} = useSelector(
    (state: RootState) => state.auth,
  );
  const dispatch = useDispatch();
  const nav = useNavigation<NavProp>();
  const [err, setErr] = useState(false);

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.cont}
      showsVerticalScrollIndicator={false}>
      {loginUser ? (
        <React.Fragment>
          <Image
            source={err ? IMAGES.profile : {uri: userDetails?.profile_picture}}
            style={styles.profile}
            onError={() => setErr(!err)}
          />
          <Text style={styles.name}>
            {userDetails?.first_name + ' ' + userDetails?.last_name}
          </Text>
          <Text style={styles.email}>{userDetails?.email}</Text>
          <CustomButton
            auth
            title={'Edit Profile'}
            onPress={() => nav.navigate('EditProfile')}
          />
          <CustomButton
            auth
            title={'Favourites Post'}
            onPress={() => nav.navigate('FavouritePostList')}
          />
          <CustomButton
            auth
            title={'Log Out'}
            onPress={() => dispatch(logOut())}
          />
        </React.Fragment>
      ) : (
        <View style={styles.logCont}>
          <Text style={styles.loginText}>Login to User</Text>
          <CustomButton
            title={'Login'}
            auth
            onPress={() => nav.navigate('SignIn')}
          />
        </View>
      )}
    </ScrollView>
  );
};

export default Profile;

const styles = StyleSheet.create({
  loginText: {
    color: COLORS.white,
    fontSize: SIZES.width06,
    fontFamily: FONTS.bold,
  },
  logCont: {
    justifyContent: 'center',
    alignItems: 'center',
    height: SIZES.height70,
  },
  email: {
    fontSize: SIZES.width05,
    color: COLORS.white,
    marginBottom: 15,
  },
  name: {
    fontSize: SIZES.width06,
    color: COLORS.white,
    fontFamily: FONTS.bold,
    marginBottom: 15,
  },
  profile: {
    resizeMode: 'cover',
    width: SIZES.widthHalf,
    height: SIZES.widthHalf,
    marginBottom: SIZES.width05,
    borderRadius: 100,
  },
  cont: {
    alignItems: 'center',
  },
  container: {
    height: SIZES.height,
    padding: SIZES.width05,
    backgroundColor: COLORS.primary,
  },
});
