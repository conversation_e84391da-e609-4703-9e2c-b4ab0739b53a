import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  ImageBackground,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { COLORS } from '../Constants/COLORS';

const { width, height } = Dimensions.get('window');

const topics = [
  {
    id: '1',
    title: 'Find Peace',
    image: require('../../Src/assets/Image/peace.png'),
  },
  {
    id: '2',
    title: 'Transform Your Prayer Life',
    image: require('../../Src/assets/Image/prayer.png'),
  },
  {
    id: '3',
    title: 'Increase Happiness',
    image: require('../../Src/assets/Image/MasGroup.png'),
  },
  {
    id: '4',
    title: 'Reduce Anxiety',
    image: require('../../Src/assets/Image/anxiety.png'),
  },
  {
    id: '5',
    title: 'Personal Growth',
    image: require('../../Src/assets/Image/Growth.png'),  },
  {
    id: '6',
    title: 'Better Sleep',
    image: require('../../Src/assets/Image/Sleep.png'),  },
];

const ChooseTopic = () => {
    const [selected, setSelected] = useState<string | null>(null);
  
    const renderItem = ({ item }: any) => {
      return (
        <TouchableOpacity onPress={() => setSelected(item.id)}>
          <ImageBackground
            source={item.image}
            style={[
              styles.card, 
              selected === item.id && styles.selectedCard,
            ]}
            imageStyle={{ borderRadius: 16 }}
          >
            <Text style={styles.cardText}>{item.title}</Text>
          </ImageBackground>
        </TouchableOpacity>
      );
    };
  
    return (
      <View style={styles.container}>
        <Text style={styles.header}>Find God's Peace in Prayer</Text>
        <Text style={styles.subHeader}>Choose a topic to focus on:</Text>
  
        <FlatList
          data={topics}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          numColumns={2}
          contentContainerStyle={{ paddingBottom: 20 }}
          columnWrapperStyle={{ justifyContent: 'space-between', marginTop: 15 }}
          showsVerticalScrollIndicator={false}
        />
      </View>
    );
  };
export default ChooseTopic;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.blue2,
        paddingTop: height * 0.1, 
        paddingHorizontal: width * 0.04, 
      },
      header: {
        color: COLORS.white,
        fontSize: width * 0.10, 
        fontWeight: 'bold',
        marginBottom: height * 0.015,
      },
      subHeader: {
        color: COLORS.white,
        fontSize: width * 0.06,
        marginBottom: height * 0.025,
      },
      card: {
        width: width * 0.44,
        height: height * 0.27,
        borderRadius: 16,
        overflow: 'hidden',
        justifyContent: 'flex-end',
        padding: width * 0.03,
      },
      selectedCard: {
        borderWidth: 2,
        borderColor: COLORS.white,
      },
      cardText: {
        color: 'black',
        fontSize: width * 0.06,
        fontWeight: '600',
      },
    });