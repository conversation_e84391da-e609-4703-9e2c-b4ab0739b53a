import React, {useState} from 'react';
import {
  ImageBackground,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import {IMAGES} from '../Constants/IMAGES';
import BackButton from '../Componants/BackButton';
import Loader from '../Componants/Loader';
import {SIZES} from '../Constants/SIZES';
import {COLORS} from '../Constants/COLORS';
import {FONTS} from '../Constants/FONTS';
import CustomTextInput from '../Componants/CustomInput';
import CustomButton from '../Componants/CustomButton';
import {SendCodeAPI, SubmitOtpAPI} from '../Store/Actions/AuthActions';
import {isValidEmail, showError} from '../Constants/FlashMessage';
import {useNavigation} from '@react-navigation/native';

export default function ForgotPassword() {
  const [load, setLoad] = useState(false);
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [first, setFirst] = useState(false);

  const navigation = useNavigation<any>();

  const sendCode = async () => {
    if (isValidEmail(email.trim().toLowerCase())) {
      showError('Enter valid email');
      return;   
    }
    const dataApi = {
      email: email.trim().toLowerCase(),
    };
    setLoad(true);
    SendCodeAPI(dataApi, setLoad, setFirst);
  };

  const updatePass = () => {
    if (!code) {
      showError('Enter the code');
      return;
    }
    const dataApi = {
      email: email.trim().toLowerCase(),
      otp: code, 
    };
    setLoad(true);

    SubmitOtpAPI(dataApi, setLoad, () => {
      navigation.navigate('ConfirmPassword', { email });

    });
  };

 
  
  return (
    
    
    <ImageBackground source={IMAGES.signUpBg} style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        scrollEnabled
        contentContainerStyle={styles.cont}>
        <BackButton />
        <Text style={styles.title}>Reset Your Password</Text>
        {load ? (
          <Loader />
        ) : (
          <React.Fragment>
            <CustomTextInput
              placeholder={'Email address'}
              placeholderTextColor={COLORS.gray4}
              value={email}
              editable={!first}
              setValue={setEmail}
            />
            {first && (
              <CustomTextInput
                placeholder={'Code'}
                placeholderTextColor={COLORS.gray4}
                value={code}
                setValue={setCode}
              />
            )}
          </React.Fragment>
        )}
      </ScrollView>
      <View style={styles.btnView}>
        <CustomButton
          title={first ? 'Submit OTP' : 'Send Code'}
          auth
          onPress={() => (first ? updatePass() : sendCode())}
        />
      </View>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  btnView: {
    position: 'absolute',
    bottom: SIZES.height15,
    left: 0,
    right: 0,
  },
  title: {
    fontSize: SIZES.width06,
    color: COLORS.white,
    fontFamily: FONTS.bold,
    marginVertical: SIZES.width05,
    textAlign: 'center',
  },
  cont: {
    paddingBottom: SIZES.height10,
  },
  container: {
    height: SIZES.height,
    width: SIZES.width,
    resizeMode: 'cover',
    padding: SIZES.width05,
  },
});
