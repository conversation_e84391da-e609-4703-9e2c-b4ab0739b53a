import React, {useEffect, useState} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  ImageBackground,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Loader from '../Componants/Loader';
import {COLORS} from '../Constants/COLORS';
import {
  CommunityInfoAPI,
  CommunityJoinAPI,
} from '../Store/Actions/CommunityActions';
import BackButton from '../Componants/BackButton';
import {SIZES} from '../Constants/SIZES';
import {ICONS} from '../Constants/IMAGES';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../Navigation/types';
import {useSelector} from 'react-redux';
import {RootState} from '../Store/types';

const {width, height} = Dimensions.get('window');

type NavProp = NativeStackNavigationProp<RootStackParamList>;

const ViewCommunityScreen = ({route}: any) => {
  const {id} = route.params;
  const userId = useSelector((state: RootState) => state.auth.userDetails);
  const nav = useNavigation<NavProp>();
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  console.log('userId', userId?.id);

  useEffect(() => {
    CommunityInfoAPI(id, setData, setLoading);
  }, [id]);

  const renderPost = ({item}: any) => (
    <View style={styles.postWrapper}>
      <Image
        source={{uri: item.image}}
        style={styles.postImage}
        resizeMode="cover"
      />
      <View style={{padding: 10}}>
        <Text style={styles.postTextSimple}>{item.text}</Text>
      </View>
    </View>
  );

  const closeModal = () => {
    nav.goBack();
  };

  const leaveCom = async () => {
    const APIdata = {
      user_id: userId?.id,
      community_id: id,
      status: 0,
    };
    await CommunityJoinAPI(APIdata, setLoading, nav, closeModal);
  };

  return (
    <View style={styles.container}>
      {loading ? (
        <Loader />
      ) : (
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.headerContainer}>
            <ImageBackground source={{uri: data?.image}} style={styles.image}>
              <BackButton />
              <View style={styles.row}>
                <TouchableOpacity
                  style={[styles.plusTouch, {marginRight: 15}]}
                  onPress={leaveCom}>
                  <Image source={ICONS.exit} style={styles.plusIcon} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.plusTouch}
                  onPress={() =>
                    nav.navigate('CreateCommunityPostScreen', {id: id})
                  }>
                  <Image source={ICONS.plus} style={styles.plusIcon} />
                </TouchableOpacity>
              </View>
            </ImageBackground>
            <Text style={styles.title}>{data?.name}</Text>
            <Text style={styles.description}>{data?.description}</Text>
            <Text style={styles.categoryNameLeft}>
              {data?.community_category.name}
            </Text>

            {data?.reasons?.map((item: any, index: number) => (
              <Text key={index} style={styles.reasonItem}>
                • {item.description}
              </Text>
            ))}
          </View>
          <FlatList
            data={data?.community_posts || []}
            keyExtractor={item => item.id.toString()}
            renderItem={renderPost}
            contentContainerStyle={styles.scrollContainer}
            showsVerticalScrollIndicator={false}
            scrollEnabled={false}
            ItemSeparatorComponent={() => <View style={{margin: 10}} />}
          />
        </ScrollView>
      )}
    </View>
  );
};

export default ViewCommunityScreen;

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  plusIcon: {
    resizeMode: 'contain',
    width: width * 0.06,
    height: width * 0.06,
    tintColor: COLORS.white,
  },
  plusTouch: {
    alignSelf: 'flex-start',
    borderWidth: 1,
    borderColor: COLORS.white,
    padding: width * 0.025,
    borderRadius: width * 0.075,
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.primary,
  },
  scrollContainer: {
    paddingBottom: height * 0.05,
  },
  headerContainer: {
    paddingHorizontal: width * 0.05,
    marginBottom: height * 0.03,
  },
  title: {
    fontSize: width * 0.08,
    fontWeight: 'bold',
    color: COLORS.white,
    marginBottom: height * 0.01,
    marginTop: height * 0.015,
  },
  description: {
    fontSize: width * 0.06,
    color: COLORS.white,
    marginBottom: height * 0.015,
    marginTop: height * 0.01,
  },
  image: {
    width: width,
    height: height * 0.3,
    borderRadius: 10,
    marginBottom: height * 0.02,
    borderBottomWidth: 1,
    borderColor: COLORS.white,
    alignSelf: 'center',
    padding: width * 0.05,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  categoryContainer: {
    marginBottom: height * 0.02,
    backgroundColor: COLORS.white,
    padding: width * 0.03,
    borderRadius: 8,
  },
  categoryImage: {
    width: '100%',
    height: height * 0.13,
    borderRadius: 8,
    marginBottom: height * 0.01,
  },
  categoryNameLeft: {
    fontSize: width * 0.045,
    color: COLORS.white,
    fontWeight: 'bold',
    textAlign: 'left',
  },
  reasonItem: {
    fontSize: width * 0.05,
    color: COLORS.white,
    marginBottom: height * 0.01,
  },
  postWrapper: {
    marginBottom: height * 0.02,
    backgroundColor: COLORS.white,
    width: SIZES.width90,
    alignSelf: 'center',
    borderRadius: 10,
  },
  postImage: {
    width: width * 0.9,
    height: height * 0.19,
    borderRadius: 8,
    alignSelf: 'center',
    marginBottom: height * 0.015,
  },
  postTextSimple: {
    fontSize: width * 0.048,
    color: COLORS.black,
    textAlign: 'left',
  },
});
