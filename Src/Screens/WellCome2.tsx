import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ImageBackground,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {IMAGES} from '../Constants/IMAGES';
import {COLORS} from '../Constants/COLORS';
import {SIZES} from '../Constants/SIZES';
import CustomButton from '../Componants/CustomButton';

const Welcome2 = () => {
  const navigation = useNavigation<any>();

  return (
    <ImageBackground source={IMAGES.welcome2Bg} style={styles.bgImage}>
      <Image source={IMAGES.sameemlogo} style={styles.logo} />
      <Image source={IMAGES.lamp} style={styles.fullImage} />
      <Text style={styles.heading}>We are what we do</Text>
      <Text style={styles.subheading}>
        Thousand of people are using silent moon{'\n'}for smalls Prayer &
        Meditation
      </Text>
      <View style={{margin: SIZES.width02}} />
      <CustomButton
        title={'SIGN UP'}
        auth
        bgColor={COLORS.purple}
        color={COLORS.white}
        onPress={() => navigation.navigate('SignUp')}
      />
      <TouchableOpacity
        style={styles.alreadyText}
        onPress={() => navigation.navigate('SignIn')}>
        <Text style={styles.bottomText}>ALREADY HAVE AN ACCOUNT? </Text>
        <Text style={styles.logText}> LOG IN</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={{margin: 10, alignSelf: 'center'}}
        onPress={() => navigation.navigate('BottomNavigation')}>
        <Text style={styles.bottomText}>Remind me later</Text>
      </TouchableOpacity>
    </ImageBackground>
  );
};

export default Welcome2;

const styles = StyleSheet.create({
  logText: {
    color: COLORS.purple,
  },
  alreadyText: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
  },
  bgImage: {
    height: SIZES.height,
    width: SIZES.width,
  },
  logo: {
    width: SIZES.width70,
    height: SIZES.height20,
    resizeMode: 'contain',
    marginVertical: SIZES.width05,
    alignSelf: 'center',
  },
  fullImage: {
    width: SIZES.width,
    height: SIZES.height25,
    resizeMode: 'contain',
    marginBottom: SIZES.width04,
  },
  heading: {
    fontSize: SIZES.width07,
    fontWeight: 'bold',
    color: COLORS.white,
    marginVertical: SIZES.width07,
    textAlign: 'center',
  },
  subheading: {
    fontSize: SIZES.width04,
    color: COLORS.gray4,
    textAlign: 'center',
    marginBottom: SIZES.width05,
  },
  bottomText: {
    color: COLORS.gray4,
    fontSize: SIZES.width035,
  },
});
