import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ImageBackground,
  FlatList,
  Image,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import {ICONS, IMAGES} from '../Constants/IMAGES';
import {COLORS} from '../Constants/COLORS';
import {SIZES} from '../Constants/SIZES';
import {FONTS} from '../Constants/FONTS';
import GlobalStyles from '../Constants/GlobalStyles';
import {useNavigation} from '@react-navigation/native';
import {CategoriesAPI, featuredPostAPI, getHomeTopCards} from '../Store/Actions/UserActions';
import {RootStackParamList} from '../Navigation/types';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import Loader from '../Componants/Loader';
import PostCards from '../Componants/PostCards';
import {RootState} from '../Store/types';
import {useSelector} from 'react-redux';
import EndPoints from '../Constants/Routes'; 

const {width, height} = Dimensions.get('window');

type SignInNavProp = NativeStackNavigationProp<RootStackParamList, 'Home'>;

const Home = () => {
  const {userDetails} = useSelector((state: RootState) => state.auth);
  const nav = useNavigation<SignInNavProp>();

  const [cat, setCat] = useState<any | []>([]);
  const [post, setPost] = useState<any | []>([]);
  const [loadCat, setLoadCat] = useState(false);
  const [load, setLoad] = useState(true);
  const [topCards, setTopCards] = useState<any[]>([]);
  const [loadingTop, setLoadingTop] = useState(true);

  const [catPage, setCatPage] = useState({
    current: 1,
    total: 1,
  });

  const featured = () => {
    featuredPostAPI(setPost);  
  }; 

  const getCat = async (page: number) => {
    if (page <= catPage.total) {
      setLoadCat(true);
      CategoriesAPI(page.toString(), setCat, setCatPage, setLoadCat);
    }
  };

  useEffect(() => {
    Promise.all([
      getCat(catPage.current),
      featured(),
      getHomeTopCards(setTopCards, setLoadingTop),
    ]).finally(() => setLoad(false));
  }, []);

  const renderRecommendedItem = ({item}: {item: any}) => (
    <TouchableOpacity
      onPress={() => nav.navigate('CourseDetail', {id: item.id})}
      style={styles.recommendCard}>
      <Image source={{uri: item.image}} style={styles.recommendImage} />
      <View style={styles.recommendTextContainer}>
        <Text style={styles.recommendTitle} numberOfLines={1}>
          {item?.name}
        </Text>
      </View>
    </TouchableOpacity>
  );  

  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{paddingBottom: SIZES.width10}}>
      <Text style={styles.greeting}>
        Good Morning,{' '}
        {userDetails?.first_name
          ? `${userDetails?.first_name} ${userDetails?.last_name}`
          : 'User'}
      </Text>
      <Text style={styles.subGreeting}>We wish you have a good day</Text>

      {loadingTop ? (
        <ActivityIndicator size="small" color={COLORS.white} />
      ) : (
   <FlatList
    data={topCards}
    keyExtractor={item => item.id?.toString()}
    renderItem={({item}) => {
      const imageUrl = item?.image
        ? `https://api.lightofthewords.com/uploads/music/${item.image}`
        : null;

      return (
        <TouchableOpacity
          onPress={() => nav.navigate('Prayer', {id: item.id})}
          activeOpacity={0.8}>
   {item?.image ? (
  <ImageBackground
    source={{uri: `https://api.lightofthewords.com/uploads/music/${item.image}`}}
    style={styles.card}
    imageStyle={{borderRadius: 16}}>
    
    <Text style={styles.cardTitle}>{item?.title}</Text>

    <View style={[GlobalStyles.row, styles.margin]}>
      <Text style={styles.cardTime}>3-10 MIN</Text>

      <TouchableOpacity style={styles.startButton}>
        <Text style={styles.startButtonText}>START</Text>
      </TouchableOpacity>
    </View>

  </ImageBackground>
) : null}
        </TouchableOpacity>
      );
    }}
    horizontal
    showsHorizontalScrollIndicator={false}
    ItemSeparatorComponent={() => <View style={{margin: 10}} />}
    style={{marginTop: 20}}
  />


      )}
          <TouchableOpacity onPress={() => nav.navigate('DailyBible')}>
            <ImageBackground
              source={IMAGES.dailyBg}
              style={styles.dailyThought}>
              <View>
                <Text style={styles.dailyText}>Daily Bible</Text>
              </View>
              <View
                style={{
                  width: SIZES.width10,
                  height: SIZES.width10,
                  borderRadius: 100,
                  backgroundColor: COLORS.white,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Image
                  source={ICONS.play2}
                  style={{
                    width: SIZES.width07,
                    height: SIZES.width07,
                    resizeMode: 'contain',
                    tintColor: COLORS.purple,
                  }}
                />
              </View>
            </ImageBackground>
          </TouchableOpacity>
          <Text style={styles.sectionTitle}>Featured Post</Text>

          <FlatList
            data={post}
            renderItem={({item}) => <PostCards item={item} />}
            keyExtractor={(item, key) => key.toString()}
            horizontal
            showsHorizontalScrollIndicator={false}
            style={{marginVertical: 10}}
            ItemSeparatorComponent={() => <View style={styles.sep} />}
          />
          <Text style={styles.sectionTitle}>Recommended for you</Text>

          <FlatList
            data={cat}
            renderItem={renderRecommendedItem}
            keyExtractor={(item, key) => key.toString()}
            horizontal
            showsHorizontalScrollIndicator={false}
            style={{marginTop: 10}}
            onEndReached={() => getCat(catPage.current + 1)}
            onEndReachedThreshold={0.2}
            ListFooterComponent={
              loadCat ? (
                <ActivityIndicator size="small" color={COLORS.white} />
              ) : null
            }
          />
        
      </ScrollView>
    );
};

export default Home;

const styles = StyleSheet.create({
  sep: {
    margin: 10,
  },
  margin: {
    marginVertical: 10,
  },
  container: {
    backgroundColor: COLORS.primary,
    padding: SIZES.width05,
  },
  greeting: {
    fontSize: SIZES.width07,
    color: COLORS.white,
    fontFamily: FONTS.regular,
    width: SIZES.width90,
  },
  subGreeting: {
    color: COLORS.gray4,
    fontSize: SIZES.width05,
  },
  card: {
    width: SIZES.widthHalf,
    height: SIZES.height30,
    borderRadius: 20,
    padding: SIZES.width03,
    justifyContent: 'flex-end',
    overflow: 'hidden',
  },
  cardTitle: {
    color: COLORS.white,
    fontSize: SIZES.width05,
    fontWeight: 'bold',
  },
  cardTime: {
    color: COLORS.white,
    fontSize: SIZES.width04,
  },
  startButton: {
    backgroundColor: COLORS.white,
    paddingVertical: SIZES.width02,
    paddingHorizontal: SIZES.width04,
    borderRadius: SIZES.width05,
    alignSelf: 'flex-start',
    marginTop: SIZES.width03,
  },
  startButtonText: {
    color: COLORS.black,
    fontWeight: 'bold',
    fontSize: SIZES.width035,
  },
  dailyThought: {
    backgroundColor: COLORS.gray3,
    borderRadius: 15,
    padding: SIZES.width05,
    marginTop: SIZES.width04,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    overflow: 'hidden',
  },
  dailyText: {
    color: COLORS.white,
    fontSize: SIZES.width045,
    fontWeight: 'bold',
  },
  sectionTitle: {
    color: COLORS.white,
    fontSize: width * 0.06,
    fontWeight: 'bold',
    marginTop: height * 0.035,
  },
  recommendCard: {
    width: width * 0.51,
    borderRadius: width * 0.04,
    marginRight: width * 0.03,
  },
  recommendImage: {
    width: '100%',
    height: height * 0.15,
    borderTopLeftRadius: width * 0.04,
    borderTopRightRadius: width * 0.04,
    borderBottomEndRadius: width * 0.04,
    borderBottomLeftRadius: width * 0.04,
  },
  recommendTextContainer: {
    paddingHorizontal: width * 0.06,
    paddingVertical: height * 0.01,
  },
  recommendTitle: {
    color: COLORS.white,
    fontSize: width * 0.05,
    fontWeight: 'bold',
    marginTop: height * 0.015,
  },
});
