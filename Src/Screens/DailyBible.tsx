import React, {useEffect, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {COLORS} from '../Constants/COLORS';
import {SIZES} from '../Constants/SIZES';
import WebView from 'react-native-webview';
import BackButton from '../Componants/BackButton';
import Loader from '../Componants/Loader';

const DailyBible = () => {
  const [loading, setLoading] = useState(true);

  // const htmlContent = `
  //   <!DOCTYPE html>
  //   <html>
  //   <head>
  //     <meta name="viewport" content="width=device-width, initial-scale=1">
  //     <style>
  //       body {
  //         background-color: #040348;
  //         color: white;
  //         font-size: 40px;
  //         padding: 10px;
  //         margin: 0;
  //       }
  //     </style>
  //   </head>
  //   <body>
  //     <div id="dailyVersesWrapper"></div>
  //     <script async defer src="https://dailyverses.net/get/verse.js?language=niv"></script>
  //   </body>
  //   </html>
  // `;
  const htmlContent = `
  <!DOCTYPE html>
  <html>
  <head>
    <title>API Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        font-size: 32px;
        padding: 16px;
        background-color: #f9f9f9;
      }
      h1 {
        font-size: 32px;
        color: #333;
      }
      pre {
        font-size: 32px;
        color: #111;
        background: #eee;
        padding: 12px;
        border-radius: 6px;
        overflow-x: auto;
      }
    </style>
  </head>
  <body>
    <h1>API Test Result:</h1>
    <pre id="output">Loading...</pre>

    <script>
      const output = document.getElementById('output');
      fetch("https://service.demowebsitelinks.com:3013/")
        .then(function(res) {
          output.textContent = 'Status: ' + res.status + ' ' + res.statusText;
          return res.text();
        })
        .then(function(data) {
          output.textContent += '\\n\\nResponse:\\n' + data;
        })
        .catch(function(err) {
          output.textContent = 'Error:\\n' + err;
          console.error("Fetch Error:", err);
        });
    </script>
  </body>
  </html>
`;

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 5000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <View style={styles.container}>
      <BackButton />
      {loading ? (
        <Loader />
      ) : (
        <WebView
          originWhitelist={['*']}
          source={{html: htmlContent}}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          onLoad={() => setLoading(false)}
        />
      )}
    </View>
  );
};

export default DailyBible;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.primary,
    padding: SIZES.width05,
    height: SIZES.height,
  },
});
