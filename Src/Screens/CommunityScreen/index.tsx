import React, {useEffect, useState} from 'react';
import {
  FlatList,
  View,
  StyleSheet,
  Text,
  Image,
  Dimensions,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import {GetCommunitiesAPI} from '../../Store/Actions/AuthActions';
import {COLORS} from '../../Constants/COLORS';
import Loader from '../../Componants/Loader';
import {SIZES} from '../../Constants/SIZES';
import {ICONS} from '../../Constants/IMAGES';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../Navigation/types';
import {useSelector} from 'react-redux';
import {RootState} from '../../Store/types';
import {showError} from '../../Constants/FlashMessage';
import {userJoinedCommunities} from '../../Store/Actions/CommunityActions';
import CommunityModal from '../../Componants/CommunityModal';

const {width, height} = Dimensions.get('window');

type NavProp = NativeStackNavigationProp<RootStackParamList>;

const CommunityScreen = () => {
  const {loginUser, userDetails} = useSelector(
    (state: RootState) => state.auth,
  );
  const nav = useNavigation<NavProp>();
  const [loading, setLoading] = useState(true);
  const [communities, setCommunities] = useState<any[]>([]);
  const [joined, setJoined] = useState<any>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [comData, setComData] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);

      const promises = [
        new Promise(resolve =>
          GetCommunitiesAPI(data => {
            setCommunities(data);
            resolve(true);
          }),
        ),
      ];

      if (loginUser) {
        promises.push(userJoinedCommunities(userDetails.id, setJoined));
      }

      Promise.all(promises).finally(() => setLoading(false));
    };

    fetchData();
  }, []);

  const renderJoined = ({item}: {item: any}) => (
    <TouchableOpacity
      onPress={() =>
        nav.navigate('ViewCommunityScreen', {id: item?.community?.id})
      }
      style={styles.joinedCard}>
      <Image
        source={{uri: item?.community?.image}}
        style={styles.imageJoined}
      />
      <View style={styles.inner}>
        <Text style={styles.title2} numberOfLines={2}>
          {item?.community?.name}
        </Text>
        <Text style={styles.description} numberOfLines={2}>
          {item?.community?.description}
        </Text>

        {item.community_category && (
          <Text style={styles.category}>
            Category: {item.community_category.name}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  const openModal = (item: any) => {
    setComData(item);
    setModalVisible(true);
  };

  const renderItem = ({item}: {item: any}) => (
    <TouchableOpacity
      style={styles.communityCard}
      onPress={() => openModal(item)}>
      <Image source={{uri: item.image}} style={styles.image} />
      <View style={styles.textContainer}>
        <Text style={styles.title}>{item.name}</Text>
        <Text style={styles.description}>{item.description}</Text>

        {item.community_category && (
          <Text style={styles.category}>
            Category: {item.community_category.name}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  const navFunc = () => {
    if (!loginUser) return showError('Login to continue');
    nav.navigate('CreateCommunityScreen');
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.scrollContainer}>
      {loading ? (
        <Loader />
      ) : (
        <>
          <View style={styles.row}>
            <Text style={styles.titleMain}>Communities</Text>
            <TouchableOpacity style={styles.addIconTouch} onPress={navFunc}>
              <Image source={ICONS.plus} style={styles.addIcon} />
            </TouchableOpacity>
          </View>
          {loginUser ? (
            <>
              <Text style={styles.recom}>Joined</Text>
              <FlatList
                data={joined}
                keyExtractor={item => item.id.toString()}
                renderItem={renderJoined}
                horizontal
                showsHorizontalScrollIndicator={false}
                ItemSeparatorComponent={() => <View style={{margin: 10}} />}
              />
            </>
          ) : (
            <Text style={styles.loginText}>
              Login to see which communities you have joined
            </Text>
          )}
          <FlatList
            data={communities}
            keyExtractor={item => item.id.toString()}
            renderItem={renderItem}
            showsVerticalScrollIndicator={false}
            ItemSeparatorComponent={() => <View style={{margin: 10}} />}
            ListHeaderComponent={() => (
              <Text style={styles.recom}>Recommended</Text>
            )}
          />
        </>
      )}
      <CommunityModal
        modalVisible={modalVisible}
        setModalVisible={setModalVisible}
        comData={comData}
        setComData={setComData}
        loginUser={loginUser}
      />
    </ScrollView>
  );
};

export default CommunityScreen;

const styles = StyleSheet.create({
  loginText: {
    color: COLORS.white,
    fontSize: SIZES.width04,
  },
  inner: {
    padding: 10,
  },
  imageJoined: {
    width: '100%',
    height: SIZES.height20,
    marginRight: width * 0.03,
    resizeMode: 'cover',
  },
  joinedCard: {
    width: SIZES.width60,
    backgroundColor: COLORS.white,
    borderRadius: 15,
    overflow: 'hidden',
  },
  recom: {
    fontSize: SIZES.width045,
    color: COLORS.white,
    fontWeight: 'bold',
    marginVertical: 15,
  },
  addIconTouch: {
    padding: 10,
    borderWidth: 1,
    borderColor: COLORS.white,
    borderRadius: 100,
  },
  addIcon: {
    resizeMode: 'contain',
    width: SIZES.width05,
    height: SIZES.width05,
    tintColor: COLORS.white,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SIZES.width05,
  },
  scrollContainer: {
    paddingTop: height * 0.05,
    paddingHorizontal: width * 0.05,
    paddingBottom: height * 0.05,
  },
  titleMain: {
    color: COLORS.white,
    fontSize: width * 0.065,
    fontWeight: '600',
    marginBottom: height * 0.01,
    lineHeight: width * 0.08,
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.primary,
  },
  communityCard: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
    padding: width * 0.05,
    borderRadius: width * 0.02,
    alignItems: 'center',
  },
  image: {
    width: width * 0.2,
    height: width * 0.2,
    borderRadius: 20,
    marginRight: width * 0.03,
  },
  textContainer: {
    flex: 1,
    width: SIZES.width70,
  },
  title2: {
    fontSize: width * 0.05,
    fontWeight: 'bold',
    color: COLORS.black,
  },
  title: {
    fontSize: width * 0.045,
    fontWeight: 'bold',
    color: COLORS.black,
  },
  description: {
    fontSize: width * 0.04,
    color: COLORS.gray3,
    marginTop: height * 0.005,
  },
  category: {
    fontSize: width * 0.04,
    color: COLORS.gray3,
    marginTop: height * 0.005,
    fontStyle: 'italic',
  },
});
