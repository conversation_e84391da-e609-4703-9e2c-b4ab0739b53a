import React, {useState} from 'react';
import {
  View,
  Text,
  ImageBackground,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
} from 'react-native';
import CustomTextInput from '../Componants/CustomInput';
import CustomButton from '../Componants/CustomButton';
import {IMAGES} from '../Constants/IMAGES';
import {COLORS} from '../Constants/COLORS';
import {SIZES} from '../Constants/SIZES';
import {showError} from '../Constants/FlashMessage';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import {RootStackParamList} from '../Navigation/types';
import {ResetPasswordAPI} from '../Store/Actions/AuthActions';
import BackButton from '../Componants/BackButton';

const {height, width} = Dimensions.get('window');

type ConfirmPasswordRouteProp = RouteProp<
  RootStackParamList,
  'ConfirmPassword'
>;

const ConfirmPassword = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);   

  const navigation = useNavigation();
  const route = useRoute<ConfirmPasswordRouteProp>();
  const {email} = route.params;

  const onSubmit = () => {
    if (!password || !confirmPassword) {
      showError('Please fill both fields');    
      return;
    }

    if (password !== confirmPassword) {
      showError('Passwords do not match');
      return;
    }

    const payload = {   
      email,
      password,
    };

    setLoading(true);
    ResetPasswordAPI(payload, setLoading, navigation);
  };

  return (
    <ImageBackground source={IMAGES.signUpBg} style={styles.container}>
      <View style={{marginHorizontal: width * 0.04}}>
        <BackButton />
      </View>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={styles.inner}>
        <ScrollView contentContainerStyle={styles.content}>
          <Text style={styles.title}>Set New Password</Text>

          <CustomTextInput
            placeholder="Password"
            placeholderTextColor={COLORS.gray4}
            value={password}
            setValue={setPassword}
            secureTextEntry
          />

          <CustomTextInput
            placeholder="Confirm Password"
            placeholderTextColor={COLORS.gray4}
            value={confirmPassword}
            setValue={setConfirmPassword}
            secureTextEntry
          />

          <View style={{flex: 1, paddingTop: height * 0.44}}>
            <CustomButton title="Submit" onPress={onSubmit} auth />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </ImageBackground>
  );
};

export default ConfirmPassword;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: height * 0.04,
  },
  inner: {
    flex: 1,
    paddingHorizontal: width * 0.04,
  },
  content: {
    flexGrow: 1,
    paddingBottom: height * 0.05,
  },
  title: {
    fontSize: width * 0.06,
    fontWeight: 'bold',
    color: COLORS.white,
    marginBottom: height * 0.025,
    textAlign: 'center',
  },
});
