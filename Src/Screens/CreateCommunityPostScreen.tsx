import React, {useState} from 'react';
import {
  Image,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Loader from '../Componants/Loader';
import {COLORS} from '../Constants/COLORS';
import BackButton from '../Componants/BackButton';
import {SIZES} from '../Constants/SIZES';
import CustomTextInput from '../Componants/CustomInput';
import {
  ImageLibraryOptions,
  launchImageLibrary,
} from 'react-native-image-picker';
import CustomButton from '../Componants/CustomButton';
import {CommunityPostAPI} from '../Store/Actions/CommunityActions';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../Navigation/types';

type NavProp = NativeStackNavigationProp<RootStackParamList>;

const CreateCommunityPostScreen = ({route}: any) => {
  const {id} = route.params;
  const nav = useNavigation<NavProp>();
  const [loading, setLoading] = useState(false);
  const [name, setName] = useState('');
  const [image, setImage] = useState<any>(null);

  const pickImage = () => {
    const options: ImageLibraryOptions = {
      mediaType: 'photo',
      quality: 0.7,
    };

    launchImageLibrary(options, response => {
      if (response.didCancel) {
        console.log('User ne cancel kar diya');
      } else if (response.errorCode) {
        console.log('Error: ', response.errorMessage);
      } else if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];
        setImage({
          uri: asset?.uri,
          fileName: asset.fileName,
          type: asset.type,
        });
      }
    });
  };

  const submit = async () => {
    const formData = new FormData();
    formData.append('community_id', id);
    formData.append('text', name);
    formData.append('image', {
      uri: Platform.OS === 'ios' ? image.uri.replace('file://', '') : image.uri,
      type: image.type || 'image/jpeg',
      name: image.fileName || `image_${Date.now()}.jpg`,
    } as any);
    await CommunityPostAPI(formData, setLoading, nav);
  };

  return (
    <View style={styles.container}>
      {loading ? (
        <Loader />
      ) : (
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={styles.headerContainer}>
          <BackButton />
          <Text style={styles.title}>Create Post</Text>
          <TouchableOpacity onPress={pickImage} style={styles.imagePicker}>
            {image ? (
              <Image
                source={{uri: image.uri}}
                style={styles.imageButtonImage}
                resizeMode="cover"
              />
            ) : (
              <Text style={styles.imagePickerText}>Choose Image</Text>
            )}
          </TouchableOpacity>
          <CustomTextInput
            value={name}
            setValue={setName}
            placeholder={'Title'}
            placeholderTextColor={COLORS.gray5}
          />
          <CustomButton auth title={'Create Post'} onPress={submit} />
        </ScrollView>
      )}
    </View>
  );
};

export default CreateCommunityPostScreen;

const styles = StyleSheet.create({
  imageButtonImage: {
    width: '100%',
    height: '100%',
  },
  imagePicker: {
    width: SIZES.width90,
    height: SIZES.height20,
    backgroundColor: COLORS.gray,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
    marginVertical: 10,
  },
  imagePickerText: {
    color: COLORS.gray5,
    fontSize: SIZES.width04,
    fontWeight: '500',
  },
  title: {
    fontSize: SIZES.width07,
    fontWeight: 'bold',
    color: COLORS.white,
    marginVertical: SIZES.width03,
  },
  headerContainer: {
    padding: SIZES.width05,
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.primary,
  },
});
