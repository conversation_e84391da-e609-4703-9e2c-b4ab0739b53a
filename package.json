{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.2", "@react-native-community/slider": "^4.5.7", "@react-navigation/bottom-tabs": "^7.3.17", "@react-navigation/native": "^7.1.11", "@react-navigation/native-stack": "^7.3.16", "@reduxjs/toolkit": "^2.8.2", "@types/react-native-vector-icons": "^6.4.18", "axios": "^1.10.0", "i": "^0.3.7", "i18next": "^25.2.1", "npm": "^11.4.2", "react": "19.0.0", "react-i18next": "^15.5.2", "react-native": "0.79.3", "react-native-dropdown-picker": "^5.4.6", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "^2.26.0", "react-native-image-picker": "^8.2.1", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-sound-player": "^0.14.5", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.15.0", "react-native-webview": "^13.15.0", "react-native-youtube-iframe": "^2.3.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "^18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.3", "@react-native/eslint-config": "0.79.3", "@react-native/metro-config": "0.79.3", "@react-native/typescript-config": "0.79.3", "@types/jest": "^29.5.13", "@types/react": "^19.1.9", "@types/react-native": "^0.72.8", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}